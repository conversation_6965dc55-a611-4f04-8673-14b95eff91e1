<?php

namespace App\Http\Controllers;

use App\Models\Fine;
use App\Models\ActivityLog;
use Illuminate\Http\Request;

class FineController extends Controller
{
    public function index()
    {
        $fines = Fine::with(['user', 'transaction.book', 'paidByStaff'])
            ->when(request('search'), function ($query, $search) {
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('student_id', 'like', "%{$search}%");
                })
                ->orWhereHas('transaction', function ($q) use ($search) {
                    $q->where('transaction_code', 'like', "%{$search}%");
                });
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->when(request('reason'), function ($query, $reason) {
                $query->where('reason', $reason);
            })
            ->when(request('date_from'), function ($query, $date) {
                $query->whereDate('created_at', '>=', $date);
            })
            ->when(request('date_to'), function ($query, $date) {
                $query->whereDate('created_at', '<=', $date);
            })
            ->latest()
            ->paginate(20);

        $stats = [
            'total_fines' => Fine::sum('amount'),
            'unpaid_fines' => Fine::where('status', 'belum_bayar')->sum('amount'),
            'paid_fines' => Fine::where('status', 'sudah_bayar')->sum('amount'),
            'waived_fines' => Fine::where('status', 'dibebaskan')->sum('amount'),
        ];

        return view('fines.index', compact('fines', 'stats'));
    }

    public function show(Fine $fine)
    {
        $fine->load(['user', 'transaction.book', 'paidByStaff']);
        return view('fines.show', compact('fine'));
    }

    public function pay(Request $request, Fine $fine)
    {
        if ($fine->status !== 'belum_bayar') {
            return back()->with('error', 'Denda ini sudah diproses sebelumnya.');
        }

        $oldValues = $fine->toArray();
        $fine->markAsPaid(auth()->id());

        ActivityLog::log('Memproses pembayaran denda', $fine, $oldValues, [
            'status' => 'sudah_bayar',
            'paid_date' => now(),
            'paid_by' => auth()->id(),
        ]);

        return back()->with('success', 'Pembayaran denda berhasil diproses.');
    }

    public function waive(Request $request, Fine $fine)
    {
        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        if ($fine->status !== 'belum_bayar') {
            return back()->with('error', 'Denda ini sudah diproses sebelumnya.');
        }

        $oldValues = $fine->toArray();
        
        $fine->update([
            'status' => 'dibebaskan',
            'paid_date' => now(),
            'paid_by' => auth()->id(),
            'notes' => $request->notes,
        ]);

        ActivityLog::log('Membebaskan denda', $fine, $oldValues, [
            'status' => 'dibebaskan',
            'paid_date' => now(),
            'paid_by' => auth()->id(),
            'notes' => $request->notes,
        ]);

        return back()->with('success', 'Denda berhasil dibebaskan.');
    }

    public function myFines()
    {
        $user = auth()->user();
        
        $fines = $user->fines()
            ->with(['transaction.book'])
            ->latest()
            ->paginate(20);

        $stats = [
            'total_fines' => $user->fines()->sum('amount'),
            'unpaid_fines' => $user->fines()->where('status', 'belum_bayar')->sum('amount'),
            'paid_fines' => $user->fines()->where('status', 'sudah_bayar')->sum('amount'),
        ];

        return view('fines.my-fines', compact('fines', 'stats'));
    }
}
