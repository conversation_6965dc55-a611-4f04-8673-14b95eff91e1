<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Admin User
        User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '081234567890',
            'address' => 'Jl. Admin No. 1',
            'is_active' => true,
        ]);

        // Create Staff User
        User::create([
            'name' => 'Staff Perpustakaan',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'staff',
            'phone' => '081234567891',
            'address' => 'Jl. Staff No. 2',
            'is_active' => true,
        ]);

        // Create Sample Students
        User::create([
            'name' => 'Ahmad Siswa',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'siswa',
            'phone' => '081234567892',
            'address' => 'Jl. Siswa No. 3',
            'birth_date' => '2005-01-15',
            'gender' => 'L',
            'student_id' => '2023001',
            'class' => 'XII IPA 1',
            'is_active' => true,
        ]);

        User::create([
            'name' => 'Siti Siswi',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'siswa',
            'phone' => '081234567893',
            'address' => 'Jl. Siswi No. 4',
            'birth_date' => '2005-03-20',
            'gender' => 'P',
            'student_id' => '2023002',
            'class' => 'XII IPS 1',
            'is_active' => true,
        ]);

        $this->call([
            CategorySeeder::class,
            BookSeeder::class,
            SettingSeeder::class,
        ]);
    }
}
