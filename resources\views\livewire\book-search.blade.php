<div class="space-y-6">
    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search Input -->
            <div class="md:col-span-2">
                <flux:input 
                    wire:model.live.debounce.300ms="search" 
                    placeholder="Cari judul, penulis, ISBN, atau barcode..."
                    class="w-full"
                />
            </div>
            
            <!-- Category Filter -->
            <div>
                <flux:select wire:model.live="category" placeholder="Semua Kategori">
                    <option value="">Semua Kategor<PERSON></option>
                    @foreach($categories as $cat)
                        <option value="{{ $cat->id }}">{{ $cat->name }}</option>
                    @endforeach
                </flux:select>
            </div>
            
            <!-- Condition Filter -->
            <div>
                <flux:select wire:model.live="condition" placeholder="<PERSON><PERSON><PERSON>">
                    <option value="">Se<PERSON><PERSON></option>
                    <option value="baru">Baru</option>
                    <option value="baik">Baik</option>
                    <option value="rusak">Rusak</option>
                </flux:select>
            </div>
        </div>
        
        <!-- Additional Filters -->
        <div class="mt-4 flex flex-wrap gap-4 items-center">
            <div>
                <flux:select wire:model.live="availability" placeholder="Ketersediaan">
                    <option value="">Semua</option>
                    <option value="available">Tersedia</option>
                    <option value="unavailable">Tidak Tersedia</option>
                </flux:select>
            </div>
            
            <div>
                <flux:select wire:model.live="perPage">
                    <option value="12">12 per halaman</option>
                    <option value="24">24 per halaman</option>
                    <option value="48">48 per halaman</option>
                </flux:select>
            </div>
            
            @if($search || $category || $condition || $availability)
                <flux:button wire:click="clearFilters" variant="ghost" size="sm">
                    <flux:icon.x-mark class="w-4 h-4 mr-1" />
                    Hapus Filter
                </flux:button>
            @endif
        </div>
    </div>

    <!-- Results -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <!-- Results Header -->
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Hasil Pencarian
                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                        ({{ $books->total() }} buku ditemukan)
                    </span>
                </h2>
            </div>
        </div>

        <!-- Books Grid -->
        <div class="p-6">
            @if($books->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach($books as $book)
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <!-- Book Cover -->
                            <div class="aspect-[3/4] bg-gray-200 dark:bg-gray-600 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
                                @if($book->cover_image)
                                    <img src="{{ Storage::url($book->cover_image) }}" 
                                         alt="{{ $book->title }}" 
                                         class="w-full h-full object-cover">
                                @else
                                    <flux:icon.book-open class="w-12 h-12 text-gray-400" />
                                @endif
                            </div>

                            <!-- Book Info -->
                            <div class="space-y-2">
                                <h3 class="font-medium text-gray-900 dark:text-white text-sm line-clamp-2">
                                    {{ $book->title }}
                                </h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400">
                                    {{ $book->author }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-500">
                                    {{ $book->category->name }}
                                </p>
                                
                                <!-- Availability Status -->
                                <div class="flex items-center justify-between">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($book->isAvailable()) 
                                            bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else 
                                            bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                        @endif">
                                        @if($book->isAvailable())
                                            Tersedia ({{ $book->available_stock }})
                                        @else
                                            Tidak Tersedia
                                        @endif
                                    </span>
                                    
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @if($book->condition === 'baru') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        @elseif($book->condition === 'baik') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                        @else bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @endif">
                                        {{ ucfirst($book->condition) }}
                                    </span>
                                </div>

                                <!-- Action Button -->
                                <div class="pt-2">
                                    <flux:button 
                                        href="{{ route('books.show', $book) }}" 
                                        variant="outline" 
                                        size="sm" 
                                        class="w-full">
                                        Lihat Detail
                                    </flux:button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $books->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="text-center py-12">
                    <flux:icon.book-open class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Tidak ada buku ditemukan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        Coba ubah kata kunci pencarian atau filter yang digunakan.
                    </p>
                    @if($search || $category || $condition || $availability)
                        <flux:button wire:click="clearFilters" variant="outline">
                            Hapus Semua Filter
                        </flux:button>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
