<?php

namespace Database\Seeders;

use App\Models\Book;
use Illuminate\Database\Seeder;

class BookSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $books = [
            [
                'title' => '<PERSON><PERSON>',
                'author' => '<PERSON>',
                'publisher' => 'Bentang Pustaka',
                'publication_year' => 2005,
                'isbn' => '9789793062792',
                'category_id' => 1, // Fiksi
                'description' => 'Novel tentang perjuangan anak-anak Belitung untuk mendapatkan pendidikan',
                'stock' => 5,
                'available_stock' => 5,
                'condition' => 'baik',
                'location' => 'Rak A1',
                'price' => 75000,
            ],
            [
                'title' => 'Bumi Manusia',
                'author' => 'Pramoedya Ananta Toer',
                'publisher' => 'Hasta Mitra',
                'publication_year' => 1980,
                'isbn' => '9789799731234',
                'category_id' => 1, // Fiksi
                'description' => 'Novel sejarah tentang kehidupan di masa kolonial Belanda',
                'stock' => 3,
                'available_stock' => 3,
                'condition' => 'baik',
                'location' => 'Rak A2',
                'price' => 85000,
            ],
            [
                'title' => 'Matematika SMA Kelas XII',
                'author' => 'Tim Penulis',
                'publisher' => 'Erlangga',
                'publication_year' => 2023,
                'isbn' => '9786024567890',
                'category_id' => 3, // Pendidikan
                'description' => 'Buku pelajaran matematika untuk SMA kelas XII',
                'stock' => 10,
                'available_stock' => 10,
                'condition' => 'baru',
                'location' => 'Rak B1',
                'price' => 120000,
            ],
            [
                'title' => 'Fisika Dasar',
                'author' => 'Prof. Dr. Bambang Ruwanto',
                'publisher' => 'Andi Offset',
                'publication_year' => 2022,
                'isbn' => '9786023456789',
                'category_id' => 4, // Sains
                'description' => 'Buku dasar-dasar fisika untuk pemula',
                'stock' => 7,
                'available_stock' => 7,
                'condition' => 'baik',
                'location' => 'Rak C1',
                'price' => 95000,
            ],
            [
                'title' => 'Sejarah Indonesia Modern',
                'author' => 'Dr. Ricklefs',
                'publisher' => 'Serambi',
                'publication_year' => 2021,
                'isbn' => '9786021234567',
                'category_id' => 5, // Sejarah
                'description' => 'Sejarah Indonesia dari masa kolonial hingga modern',
                'stock' => 4,
                'available_stock' => 4,
                'condition' => 'baik',
                'location' => 'Rak D1',
                'price' => 110000,
            ],
            [
                'title' => 'Pemrograman Web dengan Laravel',
                'author' => 'Andi Prasetyo',
                'publisher' => 'Informatika',
                'publication_year' => 2023,
                'isbn' => '9786025123456',
                'category_id' => 9, // Komputer
                'description' => 'Panduan lengkap pemrograman web menggunakan framework Laravel',
                'stock' => 6,
                'available_stock' => 6,
                'condition' => 'baru',
                'location' => 'Rak E1',
                'price' => 135000,
            ],
            [
                'title' => 'Bahasa Inggris untuk SMA',
                'author' => 'Tim English Master',
                'publisher' => 'Gramedia',
                'publication_year' => 2023,
                'isbn' => '9786026789012',
                'category_id' => 10, // Bahasa
                'description' => 'Buku pembelajaran bahasa Inggris untuk tingkat SMA',
                'stock' => 8,
                'available_stock' => 8,
                'condition' => 'baru',
                'location' => 'Rak F1',
                'price' => 98000,
            ],
            [
                'title' => 'Ensiklopedia Sains',
                'author' => 'Tim Sains Indonesia',
                'publisher' => 'Erlangga',
                'publication_year' => 2022,
                'isbn' => '9786027890123',
                'category_id' => 8, // Referensi
                'description' => 'Ensiklopedia lengkap tentang berbagai bidang sains',
                'stock' => 2,
                'available_stock' => 2,
                'condition' => 'baik',
                'location' => 'Rak G1',
                'price' => 250000,
            ],
        ];

        foreach ($books as $index => $book) {
            $book['barcode'] = 'BK2024' . str_pad($index + 1, 6, '0', STR_PAD_LEFT);
            Book::create($book);
        }
    }
}
