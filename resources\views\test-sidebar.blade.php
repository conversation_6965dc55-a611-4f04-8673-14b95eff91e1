<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <div class="flex">
            <!-- Test Sidebar -->
            <div class="w-64 bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700 min-h-screen p-4">
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Test Navigation</h2>
                    
                    <nav class="space-y-2">
                        <a href="{{ route('dashboard') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                            <span>🏠</span>
                            <span>Dashboard</span>
                        </a>
                        
                        <a href="{{ route('books.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                            <span>📚</span>
                            <span>Daftar Buku</span>
                        </a>
                        
                        @if(auth()->user()->isAdmin())
                            <a href="{{ route('categories.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>🏷️</span>
                                <span>Kategori</span>
                            </a>
                        @endif
                        
                        @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
                            <a href="{{ route('transactions.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>🔄</span>
                                <span>Peminjaman</span>
                            </a>
                            
                            <a href="{{ route('users.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>👥</span>
                                <span>Anggota</span>
                            </a>
                            
                            <a href="{{ route('fines.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>💰</span>
                                <span>Denda</span>
                            </a>
                            
                            <a href="{{ route('reports.index') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>📊</span>
                                <span>Laporan</span>
                            </a>
                        @endif
                        
                        @if(auth()->user()->isSiswa())
                            <a href="{{ route('my-transactions') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>📖</span>
                                <span>Riwayat Pinjam</span>
                            </a>
                            
                            <a href="{{ route('my-fines') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                                <span>💸</span>
                                <span>Denda Saya</span>
                            </a>
                        @endif
                        
                        <a href="{{ route('debug') }}" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                            <span>🔧</span>
                            <span>Debug Info</span>
                        </a>
                    </nav>
                    
                    <div class="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <p><strong>User:</strong> {{ auth()->user()->name }}</p>
                            <p><strong>Role:</strong> {{ auth()->user()->role }}</p>
                        </div>
                        
                        <form method="POST" action="{{ route('logout') }}" class="mt-4">
                            @csrf
                            <button type="submit" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 w-full text-left">
                                <span>🚪</span>
                                <span>Logout</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="flex-1 p-8">
                <div class="max-w-4xl mx-auto">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Test Sidebar</h1>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Navigation Test</h2>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Ini adalah halaman test untuk memastikan navigasi berfungsi dengan baik.
                        </p>
                        
                        <div class="space-y-2">
                            <p><strong>Current User:</strong> {{ auth()->user()->name }}</p>
                            <p><strong>Role:</strong> {{ auth()->user()->role }}</p>
                            <p><strong>Is Admin:</strong> {{ auth()->user()->isAdmin() ? 'Yes' : 'No' }}</p>
                            <p><strong>Is Staff:</strong> {{ auth()->user()->isStaff() ? 'Yes' : 'No' }}</p>
                            <p><strong>Is Siswa:</strong> {{ auth()->user()->isSiswa() ? 'Yes' : 'No' }}</p>
                        </div>
                        
                        <div class="mt-6">
                            <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Kembali ke Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @fluxScripts
    </body>
</html>
