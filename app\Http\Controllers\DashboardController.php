<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Fine;
use App\Models\Category;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        if ($user->isAdmin() || $user->isStaff()) {
            return $this->adminStaffDashboard();
        }
        
        return $this->studentDashboard();
    }

    private function adminStaffDashboard()
    {
        $stats = [
            'total_books' => Book::where('is_active', true)->count(),
            'total_members' => User::where('role', 'siswa')->where('is_active', true)->count(),
            'total_staff' => User::whereIn('role', ['admin', 'staff'])->where('is_active', true)->count(),
            'total_categories' => Category::where('is_active', true)->count(),
            'books_borrowed' => Transaction::where('status', 'dipinjam')->count(),
            'books_overdue' => Transaction::where('status', 'dipinjam')
                ->where('due_date', '<', Carbon::now())
                ->count(),
            'total_fines' => Fine::where('status', 'belum_bayar')->sum('amount'),
            'books_returned_today' => Transaction::where('status', 'dikembalikan')
                ->whereDate('return_date', Carbon::today())
                ->count(),
        ];

        $recentTransactions = Transaction::with(['user', 'book', 'staff'])
            ->latest()
            ->take(10)
            ->get();

        $overdueTransactions = Transaction::with(['user', 'book'])
            ->where('status', 'dipinjam')
            ->where('due_date', '<', Carbon::now())
            ->orderBy('due_date')
            ->take(10)
            ->get();

        $popularBooks = Book::withCount(['transactions' => function ($query) {
                $query->where('created_at', '>=', Carbon::now()->subMonth());
            }])
            ->orderBy('transactions_count', 'desc')
            ->take(10)
            ->get();

        return view('dashboard.admin', compact('stats', 'recentTransactions', 'overdueTransactions', 'popularBooks'));
    }

    private function studentDashboard()
    {
        $user = auth()->user();
        
        $stats = [
            'books_borrowed' => $user->transactions()->where('status', 'dipinjam')->count(),
            'books_returned' => $user->transactions()->where('status', 'dikembalikan')->count(),
            'total_fines' => $user->fines()->where('status', 'belum_bayar')->sum('amount'),
            'overdue_books' => $user->transactions()
                ->where('status', 'dipinjam')
                ->where('due_date', '<', Carbon::now())
                ->count(),
        ];

        $currentBorrows = $user->transactions()
            ->with(['book'])
            ->where('status', 'dipinjam')
            ->get();

        $recentTransactions = $user->transactions()
            ->with(['book'])
            ->latest()
            ->take(10)
            ->get();

        $unpaidFines = $user->fines()
            ->with(['transaction.book'])
            ->where('status', 'belum_bayar')
            ->get();

        return view('dashboard.student', compact('stats', 'currentBorrows', 'recentTransactions', 'unpaidFines'));
    }
}
