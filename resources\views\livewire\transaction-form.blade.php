<div class="space-y-6">
    <form wire:submit="save" class="space-y-6">
        <!-- User Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4"><PERSON><PERSON><PERSON></h3>
            
            <div class="relative">
                <flux:input 
                    wire:model.live.debounce.300ms="userSearch"
                    placeholder="Cari nama, NIS, atau email anggota..."
                    class="w-full"
                    wire:focus="$set('showUserDropdown', true)"
                />
                
                @if($showUserDropdown && count($users) > 0)
                    <div class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        @foreach($users as $user)
                            <div wire:click="selectUser({{ $user->id }})" 
                                 class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $user->name }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">NIS: {{ $user->student_id }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Kelas: {{ $user->class }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $user->email }}</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            @if($selectedUser)
                <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-blue-900 dark:text-blue-200">{{ $selectedUser->name }}</p>
                            <p class="text-sm text-blue-700 dark:text-blue-300">NIS: {{ $selectedUser->student_id }} | Kelas: {{ $selectedUser->class }}</p>
                        </div>
                        <flux:button wire:click="$set('selectedUser', null)" variant="ghost" size="sm">
                            <flux:icon.x-mark class="w-4 h-4" />
                        </flux:button>
                    </div>
                </div>
            @endif

            @error('selectedUser')
                <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
            @enderror
        </div>

        <!-- Book Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Pilih Buku</h3>
            
            <div class="relative">
                <flux:input 
                    wire:model.live.debounce.300ms="bookSearch"
                    placeholder="Cari judul, penulis, barcode, atau ISBN..."
                    class="w-full"
                    wire:focus="$set('showBookDropdown', true)"
                />
                
                @if($showBookDropdown && count($books) > 0)
                    <div class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                        @foreach($books as $book)
                            <div wire:click="selectBook({{ $book->id }})" 
                                 class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0">
                                <div class="flex items-center">
                                    <div class="w-12 h-16 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center mr-3 flex-shrink-0">
                                        @if($book->cover_image)
                                            <img src="{{ Storage::url($book->cover_image) }}" alt="{{ $book->title }}" class="w-full h-full object-cover rounded">
                                        @else
                                            <flux:icon.book-open class="w-6 h-6 text-gray-400" />
                                        @endif
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $book->title }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $book->author }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $book->category->name }}</p>
                                        <div class="flex items-center mt-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                Tersedia: {{ $book->available_stock }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>

            @if($selectedBook)
                <div class="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div class="flex items-center">
                        <div class="w-16 h-20 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center mr-4 flex-shrink-0">
                            @if($selectedBook->cover_image)
                                <img src="{{ Storage::url($selectedBook->cover_image) }}" alt="{{ $selectedBook->title }}" class="w-full h-full object-cover rounded">
                            @else
                                <flux:icon.book-open class="w-8 h-8 text-gray-400" />
                            @endif
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-green-900 dark:text-green-200">{{ $selectedBook->title }}</p>
                            <p class="text-sm text-green-700 dark:text-green-300">{{ $selectedBook->author }}</p>
                            <p class="text-sm text-green-700 dark:text-green-300">{{ $selectedBook->category->name }}</p>
                            <p class="text-sm text-green-700 dark:text-green-300">Barcode: {{ $selectedBook->barcode }}</p>
                        </div>
                        <flux:button wire:click="$set('selectedBook', null)" variant="ghost" size="sm">
                            <flux:icon.x-mark class="w-4 h-4" />
                        </flux:button>
                    </div>
                </div>
            @endif

            @error('selectedBook')
                <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
            @enderror
        </div>

        <!-- Transaction Details -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detail Peminjaman</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <flux:field>
                        <flux:label>Tanggal Pinjam</flux:label>
                        <flux:input type="date" wire:model="borrowDate" />
                        <flux:error name="borrowDate" />
                    </flux:field>
                </div>
                
                <div>
                    <flux:field>
                        <flux:label>Tanggal Jatuh Tempo</flux:label>
                        <flux:input type="date" wire:model="dueDate" />
                        <flux:error name="dueDate" />
                    </flux:field>
                </div>
                
                <div>
                    <flux:field>
                        <flux:label>Kondisi Buku</flux:label>
                        <flux:select wire:model="bookCondition">
                            <option value="baru">Baru</option>
                            <option value="baik">Baik</option>
                            <option value="rusak">Rusak</option>
                        </flux:select>
                        <flux:error name="bookCondition" />
                    </flux:field>
                </div>
                
                <div>
                    <flux:field>
                        <flux:label>Catatan (Opsional)</flux:label>
                        <flux:textarea wire:model="notes" placeholder="Catatan tambahan..." rows="3" />
                        <flux:error name="notes" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Warning Messages -->
        @if(session()->has('warning'))
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex">
                    <flux:icon.exclamation-triangle class="w-5 h-5 text-yellow-400 flex-shrink-0" />
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">{{ session('warning') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Submit Button -->
        <div class="flex justify-end space-x-3">
            <flux:button type="button" variant="ghost" href="{{ route('transactions.index') }}">
                Batal
            </flux:button>
            <flux:button type="submit" variant="primary" :disabled="!$selectedUser || !$selectedBook">
                <flux:icon.check class="w-4 h-4 mr-2" />
                Proses Peminjaman
            </flux:button>
        </div>
    </form>
</div>
