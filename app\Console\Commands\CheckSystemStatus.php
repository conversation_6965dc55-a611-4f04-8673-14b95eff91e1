<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Book;
use App\Models\Category;
use App\Models\Setting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckSystemStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'library:check-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check library system status and data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Checking Library System Status...');
        $this->newLine();

        // Check database connection
        try {
            DB::connection()->getPdo();
            $this->info('✅ Database connection: OK');
        } catch (\Exception $e) {
            $this->error('❌ Database connection: FAILED');
            $this->error('Error: ' . $e->getMessage());
            return Command::FAILURE;
        }

        // Check tables exist
        $tables = ['users', 'books', 'categories', 'transactions', 'fines', 'notifications', 'activity_logs', 'settings'];
        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                $this->info("✅ Table '{$table}': EXISTS");
            } else {
                $this->error("❌ Table '{$table}': MISSING");
            }
        }

        $this->newLine();

        // Check data
        $userCount = User::count();
        $bookCount = Book::count();
        $categoryCount = Category::count();
        $settingCount = Setting::count();

        $this->info("📊 Data Summary:");
        $this->info("   Users: {$userCount}");
        $this->info("   Books: {$bookCount}");
        $this->info("   Categories: {$categoryCount}");
        $this->info("   Settings: {$settingCount}");

        $this->newLine();

        // Check admin user
        $admin = User::where('role', 'admin')->first();
        if ($admin) {
            $this->info("👤 Admin user found: {$admin->name} ({$admin->email})");
        } else {
            $this->error("❌ No admin user found!");
        }

        // Check staff user
        $staff = User::where('role', 'staff')->first();
        if ($staff) {
            $this->info("👤 Staff user found: {$staff->name} ({$staff->email})");
        } else {
            $this->warn("⚠️  No staff user found!");
        }

        // Check student user
        $student = User::where('role', 'siswa')->first();
        if ($student) {
            $this->info("👤 Student user found: {$student->name} ({$student->email})");
        } else {
            $this->warn("⚠️  No student user found!");
        }

        $this->newLine();
        $this->info('🎉 System check completed!');

        return Command::SUCCESS;
    }
}
