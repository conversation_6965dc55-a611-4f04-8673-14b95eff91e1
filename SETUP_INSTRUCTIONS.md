# 🚀 Instruksi Setup Sistem Manajemen Perpustakaan

## 📋 Langkah-langkah Setup

### 1. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies  
npm install
```

### 2. Setup Environment
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 3. Konfigurasi Database
Edit file `.env` dan sesuaikan konfigurasi database:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=perpustakaan
DB_USERNAME=root
DB_PASSWORD=
```

### 4. Migrasi dan Seeding Database
```bash
# Run migrations
php artisan migrate

# Seed database dengan data awal
php artisan db:seed
```

### 5. Build Frontend Assets
```bash
# Build assets untuk production
npm run build

# Atau untuk development (dengan watch)
npm run dev
```

### 6. Create Storage Link
```bash
# Create symbolic link untuk file uploads
php artisan storage:link
```

### 7. Set Permissions (Linux/Mac)
```bash
# Set permissions untuk storage dan cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

### 8. Jalankan Aplikasi
```bash
# Start development server
php artisan serve
```

Aplikasi akan berjalan di: `http://localhost:8000`

## 👤 Akun Default

Setelah seeding, gunakan akun berikut untuk login:

### Admin
- **Email**: <EMAIL>
- **Password**: password

### Staff  
- **Email**: <EMAIL>
- **Password**: password

### Siswa
- **Email**: <EMAIL>
- **Password**: password

## 🔧 Troubleshooting

### Jika Menu Tidak Muncul:

1. **Cek Status Sistem:**
   ```bash
   php artisan library:check-status
   ```

2. **Akses Halaman Debug:**
   - Login ke aplikasi
   - Kunjungi: `http://localhost:8000/debug`
   - Periksa informasi user dan database

3. **Clear Cache:**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan view:clear
   php artisan route:clear
   ```

4. **Rebuild Assets:**
   ```bash
   npm run build
   ```

### Jika Database Error:

1. **Pastikan MySQL berjalan**
2. **Buat database baru:**
   ```sql
   CREATE DATABASE perpustakaan;
   ```
3. **Jalankan ulang migrasi:**
   ```bash
   php artisan migrate:fresh --seed
   ```

### Jika Permission Error:

1. **Set ownership (Linux/Mac):**
   ```bash
   sudo chown -R www-data:www-data storage
   sudo chown -R www-data:www-data bootstrap/cache
   ```

2. **Set permissions:**
   ```bash
   chmod -R 775 storage
   chmod -R 775 bootstrap/cache
   ```

## 📱 Fitur yang Tersedia

### Untuk Admin:
- ✅ Dashboard dengan statistik
- ✅ Manajemen buku dan kategori
- ✅ Manajemen anggota
- ✅ Transaksi peminjaman/pengembalian
- ✅ Manajemen denda
- ✅ Laporan dan analytics
- ✅ Pengaturan sistem

### Untuk Staff:
- ✅ Dashboard
- ✅ Manajemen buku
- ✅ Transaksi peminjaman/pengembalian
- ✅ Manajemen anggota
- ✅ Manajemen denda
- ✅ Laporan

### Untuk Siswa:
- ✅ Dashboard personal
- ✅ Pencarian buku
- ✅ Riwayat peminjaman
- ✅ Status denda

## 🔄 Scheduled Tasks (Opsional)

Untuk notifikasi otomatis, tambahkan cron job:

```bash
# Edit crontab
crontab -e

# Tambahkan baris ini:
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## 📞 Support

Jika masih ada masalah:

1. **Cek log Laravel:**
   ```bash
   tail -f storage/logs/laravel.log
   ```

2. **Cek browser console** untuk error JavaScript

3. **Pastikan semua dependencies terinstall:**
   ```bash
   composer install
   npm install
   ```

4. **Restart web server** jika diperlukan

## 🎯 Next Steps

Setelah setup berhasil:

1. Login dengan akun admin
2. Tambahkan kategori buku
3. Tambahkan buku-buku
4. Tambahkan anggota siswa
5. Mulai proses peminjaman

---

**Selamat menggunakan Sistem Manajemen Perpustakaan! 📚**
