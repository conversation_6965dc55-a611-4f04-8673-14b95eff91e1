<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\ActivityLog;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index()
    {
        $categories = Category::withCount('books')
            ->when(request('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->latest()
            ->paginate(20);

        return view('categories.index', compact('categories'));
    }

    public function create()
    {
        return view('categories.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:categories,code',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;

        $category = Category::create($validated);

        ActivityLog::log('Menambah kategori baru', $category, [], $validated);

        return redirect()->route('categories.index')->with('success', 'Kategori berhasil ditambahkan.');
    }

    public function show(Category $category)
    {
        $category->load(['books' => function ($query) {
            $query->latest()->take(10);
        }]);

        $stats = [
            'total_books' => $category->books()->count(),
            'active_books' => $category->books()->where('is_active', true)->count(),
            'available_books' => $category->books()->where('available_stock', '>', 0)->count(),
            'borrowed_books' => $category->books()
                ->whereHas('transactions', function ($query) {
                    $query->where('status', 'dipinjam');
                })
                ->count(),
        ];

        return view('categories.show', compact('category', 'stats'));
    }

    public function edit(Category $category)
    {
        return view('categories.edit', compact('category'));
    }

    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:categories,code,' . $category->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $oldValues = $category->toArray();
        $category->update($validated);

        ActivityLog::log('Mengubah data kategori', $category, $oldValues, $validated);

        return redirect()->route('categories.index')->with('success', 'Data kategori berhasil diperbarui.');
    }

    public function destroy(Category $category)
    {
        // Check if category has books
        if ($category->books()->exists()) {
            return back()->with('error', 'Tidak dapat menghapus kategori yang memiliki buku.');
        }

        $oldValues = $category->toArray();
        $category->delete();

        ActivityLog::log('Menghapus kategori', null, $oldValues, []);

        return redirect()->route('categories.index')->with('success', 'Kategori berhasil dihapus.');
    }
}
