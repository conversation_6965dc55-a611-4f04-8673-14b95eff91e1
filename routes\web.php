<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FineController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingController;
use App\Http\Middleware\RoleMiddleware;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication required routes
Route::middleware(['auth', 'verified'])->group(function () {

    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Books - accessible by all authenticated users
    Route::get('/books', [BookController::class, 'index'])->name('books.index');
    Route::get('/books/{book}', [BookController::class, 'show'])->name('books.show');
    Route::get('/books/{book}/barcode', [BookController::class, 'generateBarcode'])->name('books.barcode');

    // Admin and Staff only routes
    Route::middleware([RoleMiddleware::class . ':admin,staff'])->group(function () {

        // Book Management
        Route::get('/books/create', [BookController::class, 'create'])->name('books.create');
        Route::post('/books', [BookController::class, 'store'])->name('books.store');
        Route::get('/books/{book}/edit', [BookController::class, 'edit'])->name('books.edit');
        Route::put('/books/{book}', [BookController::class, 'update'])->name('books.update');
        Route::delete('/books/{book}', [BookController::class, 'destroy'])->name('books.destroy');

        // Transaction Management
        Route::resource('transactions', TransactionController::class);
        Route::get('/transactions/{transaction}/return', [TransactionController::class, 'return'])->name('transactions.return');
        Route::post('/transactions/{transaction}/process-return', [TransactionController::class, 'processReturn'])->name('transactions.process-return');
        Route::post('/transactions/{transaction}/extend', [TransactionController::class, 'extend'])->name('transactions.extend');

        // User Management
        Route::resource('users', UserController::class);
        Route::post('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

        // Fine Management
        Route::resource('fines', FineController::class)->only(['index', 'show', 'update']);
        Route::post('/fines/{fine}/pay', [FineController::class, 'pay'])->name('fines.pay');
        Route::post('/fines/{fine}/waive', [FineController::class, 'waive'])->name('fines.waive');

        // Reports
        Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
        Route::get('/reports/books', [ReportController::class, 'books'])->name('reports.books');
        Route::get('/reports/transactions', [ReportController::class, 'transactions'])->name('reports.transactions');
        Route::get('/reports/fines', [ReportController::class, 'fines'])->name('reports.fines');
        Route::get('/reports/members', [ReportController::class, 'members'])->name('reports.members');

    });

    // Admin only routes
    Route::middleware([RoleMiddleware::class . ':admin'])->group(function () {

        // Category Management
        Route::resource('categories', CategoryController::class);

        // System Settings
        Route::get('/system-settings', [SettingController::class, 'index'])->name('system-settings.index');
        Route::post('/system-settings', [SettingController::class, 'update'])->name('system-settings.update');

        // Advanced Reports
        Route::get('/reports/analytics', [ReportController::class, 'analytics'])->name('reports.analytics');
        Route::get('/reports/export/{type}', [ReportController::class, 'export'])->name('reports.export');

    });

    // Student specific routes
    Route::middleware([RoleMiddleware::class . ':siswa'])->group(function () {
        Route::get('/my-transactions', [TransactionController::class, 'myTransactions'])->name('my-transactions');
        Route::get('/my-fines', [FineController::class, 'myFines'])->name('my-fines');
    });

    // User Settings (Profile, Password, Appearance)
    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

});

require __DIR__.'/auth.php';
