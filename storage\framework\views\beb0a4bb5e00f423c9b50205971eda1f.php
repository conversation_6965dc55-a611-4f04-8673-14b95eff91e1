<?php if (isset($component)) { $__componentOriginal5863877a5171c196453bfa0bd807e410 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5863877a5171c196453bfa0bd807e410 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.app','data' => ['title' => __('Debug Info')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__('Debug Info'))]); ?>
    <div class="space-y-6">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Debug Information</h1>
            <p class="text-gray-600 dark:text-gray-400">System status and debugging information</p>
        </div>

        <!-- User Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current User</h2>
            <?php if(auth()->guard()->check()): ?>
                <div class="space-y-2">
                    <p><strong>Name:</strong> <?php echo e(auth()->user()->name); ?></p>
                    <p><strong>Email:</strong> <?php echo e(auth()->user()->email); ?></p>
                    <p><strong>Role:</strong> <?php echo e(auth()->user()->role); ?></p>
                    <p><strong>Is Admin:</strong> <?php echo e(auth()->user()->isAdmin() ? 'Yes' : 'No'); ?></p>
                    <p><strong>Is Staff:</strong> <?php echo e(auth()->user()->isStaff() ? 'Yes' : 'No'); ?></p>
                    <p><strong>Is Siswa:</strong> <?php echo e(auth()->user()->isSiswa() ? 'Yes' : 'No'); ?></p>
                    <p><strong>Is Active:</strong> <?php echo e(auth()->user()->is_active ? 'Yes' : 'No'); ?></p>
                </div>
            <?php else: ?>
                <p class="text-red-600">Not authenticated</p>
            <?php endif; ?>
        </div>

        <!-- Database Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Database Status</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600"><?php echo e(\App\Models\User::count()); ?></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Users</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-600"><?php echo e(\App\Models\Book::count()); ?></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Books</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-yellow-600"><?php echo e(\App\Models\Category::count()); ?></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Categories</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-purple-600"><?php echo e(\App\Models\Setting::count()); ?></p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Settings</p>
                </div>
            </div>
        </div>

        <!-- Routes Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Available Routes</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Current Route:</strong> <?php echo e(request()->route()->getName() ?? 'No name'); ?></p>
                <p><strong>Current URL:</strong> <?php echo e(request()->url()); ?></p>
                
                <div class="mt-4">
                    <h3 class="font-medium mb-2">Key Routes:</h3>
                    <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                        <li>• Dashboard: <?php echo e(route('dashboard')); ?></li>
                        <li>• Books: <?php echo e(route('books.index')); ?></li>
                        <?php if(auth()->check() && (auth()->user()->isAdmin() || auth()->user()->isStaff())): ?>
                            <li>• Transactions: <?php echo e(route('transactions.index')); ?></li>
                            <li>• Users: <?php echo e(route('users.index')); ?></li>
                            <li>• Fines: <?php echo e(route('fines.index')); ?></li>
                            <li>• Reports: <?php echo e(route('reports.index')); ?></li>
                        <?php endif; ?>
                        <?php if(auth()->check() && auth()->user()->isAdmin()): ?>
                            <li>• Categories: <?php echo e(route('categories.index')); ?></li>
                            <li>• Settings: <?php echo e(route('system-settings.index')); ?></li>
                        <?php endif; ?>
                        <?php if(auth()->check() && auth()->user()->isSiswa()): ?>
                            <li>• My Transactions: <?php echo e(route('my-transactions')); ?></li>
                            <li>• My Fines: <?php echo e(route('my-fines')); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Environment Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Environment</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Laravel Version:</strong> <?php echo e(app()->version()); ?></p>
                <p><strong>PHP Version:</strong> <?php echo e(phpversion()); ?></p>
                <p><strong>Environment:</strong> <?php echo e(app()->environment()); ?></p>
                <p><strong>Debug Mode:</strong> <?php echo e(config('app.debug') ? 'Enabled' : 'Disabled'); ?></p>
                <p><strong>Database:</strong> <?php echo e(config('database.default')); ?></p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['href' => ''.e(route('dashboard')).'','variant' => 'outline','class' => 'justify-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('dashboard')).'','variant' => 'outline','class' => 'justify-center']); ?>
                    Go to Dashboard
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['href' => ''.e(route('books.index')).'','variant' => 'outline','class' => 'justify-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('books.index')).'','variant' => 'outline','class' => 'justify-center']); ?>
                    View Books
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                <?php if(auth()->check() && (auth()->user()->isAdmin() || auth()->user()->isStaff())): ?>
                    <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['href' => ''.e(route('transactions.index')).'','variant' => 'outline','class' => 'justify-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('transactions.index')).'','variant' => 'outline','class' => 'justify-center']); ?>
                        Transactions
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['href' => ''.e(route('users.index')).'','variant' => 'outline','class' => 'justify-center']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('users.index')).'','variant' => 'outline','class' => 'justify-center']); ?>
                        Users
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $attributes = $__attributesOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__attributesOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5863877a5171c196453bfa0bd807e410)): ?>
<?php $component = $__componentOriginal5863877a5171c196453bfa0bd807e410; ?>
<?php unset($__componentOriginal5863877a5171c196453bfa0bd807e410); ?>
<?php endif; ?>
<?php /**PATH D:\FILE\website\PORTOFOLIO\perpus\resources\views/debug.blade.php ENDPATH**/ ?>