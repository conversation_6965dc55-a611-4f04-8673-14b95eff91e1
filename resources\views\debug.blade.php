<x-layouts.app :title="__('Debug Info')">
    <div class="space-y-6">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Debug Information</h1>
            <p class="text-gray-600 dark:text-gray-400">System status and debugging information</p>
        </div>

        <!-- User Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current User</h2>
            @auth
                <div class="space-y-2">
                    <p><strong>Name:</strong> {{ auth()->user()->name }}</p>
                    <p><strong>Email:</strong> {{ auth()->user()->email }}</p>
                    <p><strong>Role:</strong> {{ auth()->user()->role }}</p>
                    <p><strong>Is Admin:</strong> {{ auth()->user()->isAdmin() ? 'Yes' : 'No' }}</p>
                    <p><strong>Is Staff:</strong> {{ auth()->user()->isStaff() ? 'Yes' : 'No' }}</p>
                    <p><strong>Is Siswa:</strong> {{ auth()->user()->isSiswa() ? 'Yes' : 'No' }}</p>
                    <p><strong>Is Active:</strong> {{ auth()->user()->is_active ? 'Yes' : 'No' }}</p>
                </div>
            @else
                <p class="text-red-600">Not authenticated</p>
            @endauth
        </div>

        <!-- Database Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Database Status</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600">{{ \App\Models\User::count() }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Users</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">{{ \App\Models\Book::count() }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Books</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-yellow-600">{{ \App\Models\Category::count() }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Categories</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold text-purple-600">{{ \App\Models\Setting::count() }}</p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Settings</p>
                </div>
            </div>
        </div>

        <!-- Routes Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Available Routes</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Current Route:</strong> {{ request()->route()->getName() ?? 'No name' }}</p>
                <p><strong>Current URL:</strong> {{ request()->url() }}</p>
                
                <div class="mt-4">
                    <h3 class="font-medium mb-2">Key Routes:</h3>
                    <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                        <li>• Dashboard: {{ route('dashboard') }}</li>
                        <li>• Books: {{ route('books.index') }}</li>
                        @if(auth()->check() && (auth()->user()->isAdmin() || auth()->user()->isStaff()))
                            <li>• Transactions: {{ route('transactions.index') }}</li>
                            <li>• Users: {{ route('users.index') }}</li>
                            <li>• Fines: {{ route('fines.index') }}</li>
                            <li>• Reports: {{ route('reports.index') }}</li>
                        @endif
                        @if(auth()->check() && auth()->user()->isAdmin())
                            <li>• Categories: {{ route('categories.index') }}</li>
                            <li>• Settings: {{ route('system-settings.index') }}</li>
                        @endif
                        @if(auth()->check() && auth()->user()->isSiswa())
                            <li>• My Transactions: {{ route('my-transactions') }}</li>
                            <li>• My Fines: {{ route('my-fines') }}</li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>

        <!-- Environment Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Environment</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Laravel Version:</strong> {{ app()->version() }}</p>
                <p><strong>PHP Version:</strong> {{ phpversion() }}</p>
                <p><strong>Environment:</strong> {{ app()->environment() }}</p>
                <p><strong>Debug Mode:</strong> {{ config('app.debug') ? 'Enabled' : 'Disabled' }}</p>
                <p><strong>Database:</strong> {{ config('database.default') }}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <flux:button href="{{ route('dashboard') }}" variant="outline" class="justify-center">
                    Go to Dashboard
                </flux:button>
                <flux:button href="{{ route('books.index') }}" variant="outline" class="justify-center">
                    View Books
                </flux:button>
                @if(auth()->check() && (auth()->user()->isAdmin() || auth()->user()->isStaff()))
                    <flux:button href="{{ route('transactions.index') }}" variant="outline" class="justify-center">
                        Transactions
                    </flux:button>
                    <flux:button href="{{ route('users.index') }}" variant="outline" class="justify-center">
                        Users
                    </flux:button>
                @endif
            </div>
        </div>
    </div>
</x-layouts.app>
