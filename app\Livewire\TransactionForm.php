<?php

namespace App\Livewire;

use App\Models\Book;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Setting;
use Livewire\Component;
use Carbon\Carbon;

class TransactionForm extends Component
{
    public $selectedUser = null;
    public $selectedBook = null;
    public $userSearch = '';
    public $bookSearch = '';
    public $borrowDate;
    public $dueDate;
    public $bookCondition = 'baik';
    public $notes = '';
    
    public $showUserDropdown = false;
    public $showBookDropdown = false;
    public $users = [];
    public $books = [];

    protected $rules = [
        'selectedUser' => 'required',
        'selectedBook' => 'required',
        'borrowDate' => 'required|date',
        'dueDate' => 'required|date|after:borrowDate',
        'bookCondition' => 'required|in:baru,baik,rusak',
        'notes' => 'nullable|string|max:500',
    ];

    public function mount()
    {
        $this->borrowDate = Carbon::today()->format('Y-m-d');
        $defaultLoanDays = Setting::getValue('default_loan_days', 7);
        $this->dueDate = Carbon::today()->addDays($defaultLoanDays)->format('Y-m-d');
    }

    public function updatedUserSearch()
    {
        if (strlen($this->userSearch) >= 2) {
            $this->users = User::where('role', 'siswa')
                ->where('is_active', true)
                ->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->userSearch . '%')
                          ->orWhere('student_id', 'like', '%' . $this->userSearch . '%')
                          ->orWhere('email', 'like', '%' . $this->userSearch . '%');
                })
                ->limit(10)
                ->get();
            $this->showUserDropdown = true;
        } else {
            $this->users = [];
            $this->showUserDropdown = false;
        }
    }

    public function updatedBookSearch()
    {
        if (strlen($this->bookSearch) >= 2) {
            $this->books = Book::where('is_active', true)
                ->where('available_stock', '>', 0)
                ->where(function ($query) {
                    $query->where('title', 'like', '%' . $this->bookSearch . '%')
                          ->orWhere('author', 'like', '%' . $this->bookSearch . '%')
                          ->orWhere('barcode', 'like', '%' . $this->bookSearch . '%')
                          ->orWhere('isbn', 'like', '%' . $this->bookSearch . '%');
                })
                ->with('category')
                ->limit(10)
                ->get();
            $this->showBookDropdown = true;
        } else {
            $this->books = [];
            $this->showBookDropdown = false;
        }
    }

    public function selectUser($userId)
    {
        $this->selectedUser = User::find($userId);
        $this->userSearch = $this->selectedUser->name . ' (' . $this->selectedUser->student_id . ')';
        $this->showUserDropdown = false;
        
        // Check user status
        $this->checkUserEligibility();
    }

    public function selectBook($bookId)
    {
        $this->selectedBook = Book::find($bookId);
        $this->bookSearch = $this->selectedBook->title . ' - ' . $this->selectedBook->author;
        $this->showBookDropdown = false;
    }

    public function checkUserEligibility()
    {
        if (!$this->selectedUser) return;

        $messages = [];

        // Check overdue books
        $overdueCount = $this->selectedUser->transactions()
            ->where('status', 'dipinjam')
            ->where('due_date', '<', Carbon::now())
            ->count();

        if ($overdueCount > 0) {
            $messages[] = "Anggota memiliki {$overdueCount} buku yang terlambat dikembalikan.";
        }

        // Check borrowing limit
        $borrowLimit = Setting::getValue('borrow_limit', 3);
        $currentBorrows = $this->selectedUser->transactions()->where('status', 'dipinjam')->count();
        
        if ($currentBorrows >= $borrowLimit) {
            $messages[] = "Anggota sudah mencapai batas maksimal peminjaman ({$borrowLimit} buku).";
        }

        // Check unpaid fines
        $unpaidFines = $this->selectedUser->fines()->where('status', 'belum_bayar')->sum('amount');
        if ($unpaidFines > 0) {
            $messages[] = "Anggota memiliki denda yang belum dibayar sebesar Rp " . number_format($unpaidFines, 0, ',', '.');
        }

        if (!empty($messages)) {
            session()->flash('warning', implode(' ', $messages));
        }
    }

    public function save()
    {
        $this->validate();

        if (!$this->selectedUser || !$this->selectedBook) {
            session()->flash('error', 'Silakan pilih anggota dan buku terlebih dahulu.');
            return;
        }

        // Final checks
        if (!$this->selectedBook->isAvailable()) {
            session()->flash('error', 'Buku tidak tersedia untuk dipinjam.');
            return;
        }

        $overdueCount = $this->selectedUser->transactions()
            ->where('status', 'dipinjam')
            ->where('due_date', '<', Carbon::now())
            ->count();

        if ($overdueCount > 0) {
            session()->flash('error', 'Anggota memiliki buku yang terlambat dikembalikan.');
            return;
        }

        $borrowLimit = Setting::getValue('borrow_limit', 3);
        $currentBorrows = $this->selectedUser->transactions()->where('status', 'dipinjam')->count();
        
        if ($currentBorrows >= $borrowLimit) {
            session()->flash('error', "Anggota sudah mencapai batas maksimal peminjaman ({$borrowLimit} buku).");
            return;
        }

        // Create transaction
        $transaction = Transaction::create([
            'transaction_code' => Transaction::generateTransactionCode(),
            'user_id' => $this->selectedUser->id,
            'book_id' => $this->selectedBook->id,
            'staff_id' => auth()->id(),
            'borrow_date' => $this->borrowDate,
            'due_date' => $this->dueDate,
            'status' => 'dipinjam',
            'book_condition_borrow' => $this->bookCondition,
            'notes' => $this->notes,
        ]);

        // Update book stock
        $this->selectedBook->decrement('available_stock');

        session()->flash('success', 'Peminjaman berhasil diproses dengan kode: ' . $transaction->transaction_code);
        
        return redirect()->route('transactions.show', $transaction);
    }

    public function render()
    {
        return view('livewire.transaction-form');
    }
}
