<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Book extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'author',
        'publisher',
        'publication_year',
        'isbn',
        'barcode',
        'category_id',
        'description',
        'stock',
        'available_stock',
        'condition',
        'location',
        'cover_image',
        'price',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'publication_year' => 'integer',
            'stock' => 'integer',
            'available_stock' => 'integer',
            'price' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the category that owns the book
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get book transactions
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Check if book is available for borrowing
     */
    public function isAvailable(): bool
    {
        return $this->available_stock > 0 && $this->is_active && $this->condition !== 'hilang';
    }

    /**
     * Get current borrowers count
     */
    public function currentBorrowersCount(): int
    {
        return $this->transactions()->where('status', 'dipinjam')->count();
    }

    /**
     * Update available stock
     */
    public function updateAvailableStock(): void
    {
        $borrowed = $this->currentBorrowersCount();
        $this->update(['available_stock' => $this->stock - $borrowed]);
    }
}
