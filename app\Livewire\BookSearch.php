<?php

namespace App\Livewire;

use App\Models\Book;
use App\Models\Category;
use Livewire\Component;
use Livewire\WithPagination;

class BookSearch extends Component
{
    use WithPagination;

    public $search = '';
    public $category = '';
    public $condition = '';
    public $availability = '';
    public $perPage = 12;

    protected $queryString = [
        'search' => ['except' => ''],
        'category' => ['except' => ''],
        'condition' => ['except' => ''],
        'availability' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategory()
    {
        $this->resetPage();
    }

    public function updatingCondition()
    {
        $this->resetPage();
    }

    public function updatingAvailability()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->category = '';
        $this->condition = '';
        $this->availability = '';
        $this->resetPage();
    }

    public function render()
    {
        $books = Book::with(['category'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('author', 'like', '%' . $this->search . '%')
                      ->orWhere('isbn', 'like', '%' . $this->search . '%')
                      ->orWhere('barcode', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->category, function ($query) {
                $query->where('category_id', $this->category);
            })
            ->when($this->condition, function ($query) {
                $query->where('condition', $this->condition);
            })
            ->when($this->availability, function ($query) {
                if ($this->availability === 'available') {
                    $query->where('available_stock', '>', 0)->where('is_active', true);
                } elseif ($this->availability === 'unavailable') {
                    $query->where(function ($q) {
                        $q->where('available_stock', '<=', 0)->orWhere('is_active', false);
                    });
                }
            })
            ->where('is_active', true)
            ->orderBy('title')
            ->paginate($this->perPage);

        $categories = Category::where('is_active', true)->orderBy('name')->get();

        return view('livewire.book-search', [
            'books' => $books,
            'categories' => $categories,
        ]);
    }
}
