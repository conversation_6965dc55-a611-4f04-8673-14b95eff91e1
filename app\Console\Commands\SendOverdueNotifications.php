<?php

namespace App\Console\Commands;

use App\Models\Transaction;
use App\Models\Fine;
use App\Models\Setting;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SendOverdueNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'library:send-overdue-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send overdue notifications and create fines for late returns';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService): int
    {
        $overdueTransactions = Transaction::with(['user', 'book'])
            ->where('status', 'dipinjam')
            ->where('due_date', '<', Carbon::now())
            ->get();

        $notificationCount = 0;
        $fineCount = 0;

        foreach ($overdueTransactions as $transaction) {
            try {
                // Check if fine already exists
                $existingFine = Fine::where('transaction_id', $transaction->id)->first();
                
                if (!$existingFine) {
                    // Create fine
                    $daysLate = $transaction->daysLate();
                    $dailyRate = Setting::getValue('fine_daily_rate', 1000);
                    $amount = $daysLate * $dailyRate;

                    Fine::create([
                        'transaction_id' => $transaction->id,
                        'user_id' => $transaction->user_id,
                        'amount' => $amount,
                        'days_late' => $daysLate,
                        'daily_rate' => $dailyRate,
                        'reason' => 'terlambat',
                        'status' => 'belum_bayar',
                    ]);

                    $fineCount++;
                    $this->info("Fine created for {$transaction->user->name}: Rp {$amount}");
                }

                // Send notification (check interval)
                $reminderInterval = Setting::getValue('overdue_reminder_interval', 3);
                $lastNotification = $transaction->user->notifications()
                    ->where('type', 'overdue')
                    ->where('metadata->transaction_id', $transaction->id)
                    ->latest()
                    ->first();

                $shouldSendNotification = !$lastNotification || 
                    $lastNotification->created_at->diffInDays(Carbon::now()) >= $reminderInterval;

                if ($shouldSendNotification) {
                    $notificationService->sendOverdueNotification($transaction);
                    $notificationCount++;
                    $this->info("Overdue notification sent to {$transaction->user->name}");
                }

            } catch (\Exception $e) {
                $this->error("Failed to process overdue transaction for {$transaction->user->name}: " . $e->getMessage());
            }
        }

        $this->info("Total fines created: {$fineCount}");
        $this->info("Total overdue notifications sent: {$notificationCount}");
        
        return Command::SUCCESS;
    }
}
