<x-layouts.app :title="__('Dashboard')">
    <div class="space-y-6">
        <!-- Header -->
        <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard Siswa</h1>
            <p class="text-gray-600 dark:text-gray-400">Selamat datang, {{ auth()->user()->name }}! Be<PERSON>ut adalah ringkasan aktivitas perpustakaan Anda.</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <flux:icon.book-open class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sedang Dipinjam</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $stats['books_borrowed'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <flux:icon.check-circle class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Dikembalikan</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $stats['books_returned'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                        <flux:icon.exclamation-triangle class="w-6 h-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Buku Terlambat</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $stats['overdue_books'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <flux:icon.currency-dollar class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Denda</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">Rp {{ number_format($stats['total_fines'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert for Overdue Books -->
        @if($stats['overdue_books'] > 0)
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex">
                    <flux:icon.exclamation-triangle class="w-5 h-5 text-red-400" />
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Perhatian! Anda memiliki {{ $stats['overdue_books'] }} buku yang terlambat dikembalikan.
                        </h3>
                        <p class="mt-1 text-sm text-red-700 dark:text-red-300">
                            Segera kembalikan buku untuk menghindari denda tambahan.
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Alert for Unpaid Fines -->
        @if($stats['total_fines'] > 0)
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex">
                    <flux:icon.currency-dollar class="w-5 h-5 text-yellow-400" />
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Anda memiliki denda yang belum dibayar sebesar Rp {{ number_format($stats['total_fines'], 0, ',', '.') }}
                        </h3>
                        <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                            Silakan hubungi petugas perpustakaan untuk melakukan pembayaran.
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Current Borrows -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Buku yang Sedang Dipinjam</h2>
                    <flux:button variant="ghost" size="sm" href="{{ route('my-transactions') }}">
                        Lihat Semua
                    </flux:button>
                </div>
                <div class="space-y-4">
                    @forelse($currentBorrows as $transaction)
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-12 h-16 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center mr-3">
                                    @if($transaction->book->cover_image)
                                        <img src="{{ Storage::url($transaction->book->cover_image) }}" alt="{{ $transaction->book->title }}" class="w-full h-full object-cover rounded">
                                    @else
                                        <flux:icon.book-open class="w-6 h-6 text-gray-400" />
                                    @endif
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $transaction->book->title }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $transaction->book->author }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Dipinjam: {{ $transaction->borrow_date->format('d/m/Y') }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500 dark:text-gray-400">Jatuh tempo:</p>
                                <p class="text-sm font-medium 
                                    @if($transaction->isOverdue()) text-red-600 dark:text-red-400 
                                    @else text-gray-900 dark:text-white @endif">
                                    {{ $transaction->due_date->format('d/m/Y') }}
                                </p>
                                @if($transaction->isOverdue())
                                    <p class="text-xs text-red-600 dark:text-red-400">
                                        Terlambat {{ $transaction->daysLate() }} hari
                                    </p>
                                @endif
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-8">Anda tidak memiliki buku yang sedang dipinjam</p>
                    @endforelse
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Riwayat Peminjaman Terbaru</h2>
                    <flux:button variant="ghost" size="sm" href="{{ route('my-transactions') }}">
                        Lihat Semua
                    </flux:button>
                </div>
                <div class="space-y-4">
                    @forelse($recentTransactions as $transaction)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 rounded-full mr-3
                                    @if($transaction->status === 'dipinjam') bg-yellow-500
                                    @elseif($transaction->status === 'dikembalikan') bg-green-500
                                    @else bg-red-500 @endif"></div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $transaction->book->title }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $transaction->book->author }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500 dark:text-gray-500">{{ $transaction->created_at->format('d/m/Y') }}</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($transaction->status === 'dipinjam') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                    @elseif($transaction->status === 'dikembalikan') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                    {{ ucfirst($transaction->status) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-8">Belum ada riwayat peminjaman</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Unpaid Fines -->
        @if($unpaidFines->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Denda yang Belum Dibayar</h2>
                    <flux:button variant="ghost" size="sm" href="{{ route('my-fines') }}">
                        Lihat Semua
                    </flux:button>
                </div>
                <div class="space-y-4">
                    @foreach($unpaidFines as $fine)
                        <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $fine->transaction->book->title }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ ucfirst($fine->reason) }} - {{ $fine->days_late }} hari
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    Tanggal: {{ $fine->created_at->format('d/m/Y') }}
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-semibold text-red-600 dark:text-red-400">
                                    Rp {{ number_format($fine->amount, 0, ',', '.') }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Aksi Cepat</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <flux:button variant="outline" href="{{ route('books.index') }}" class="justify-start">
                    <flux:icon.magnifying-glass class="w-4 h-4 mr-2" />
                    Cari Buku
                </flux:button>
                <flux:button variant="outline" href="{{ route('my-transactions') }}" class="justify-start">
                    <flux:icon.clock class="w-4 h-4 mr-2" />
                    Riwayat Pinjam
                </flux:button>
                <flux:button variant="outline" href="{{ route('my-fines') }}" class="justify-start">
                    <flux:icon.currency-dollar class="w-4 h-4 mr-2" />
                    Denda Saya
                </flux:button>
                <flux:button variant="outline" href="{{ route('settings.profile') }}" class="justify-start">
                    <flux:icon.user class="w-4 h-4 mr-2" />
                    Profil Saya
                </flux:button>
            </div>
        </div>
    </div>
</x-layouts.app>
