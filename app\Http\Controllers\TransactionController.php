<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\Book;
use App\Models\User;
use App\Models\Fine;
use App\Models\ActivityLog;
use App\Models\Setting;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TransactionController extends Controller
{
    public function index()
    {
        $transactions = Transaction::with(['user', 'book', 'staff'])
            ->when(request('search'), function ($query, $search) {
                $query->where('transaction_code', 'like', "%{$search}%")
                    ->orWhereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('student_id', 'like', "%{$search}%");
                    })
                    ->orWhereHas('book', function ($q) use ($search) {
                        $q->where('title', 'like', "%{$search}%")
                          ->orWhere('barcode', 'like', "%{$search}%");
                    });
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->when(request('date_from'), function ($query, $date) {
                $query->whereDate('borrow_date', '>=', $date);
            })
            ->when(request('date_to'), function ($query, $date) {
                $query->whereDate('borrow_date', '<=', $date);
            })
            ->latest()
            ->paginate(20);

        return view('transactions.index', compact('transactions'));
    }

    public function create()
    {
        $books = Book::where('is_active', true)
            ->where('available_stock', '>', 0)
            ->get();
        
        $members = User::where('role', 'siswa')
            ->where('is_active', true)
            ->get();

        return view('transactions.create', compact('books', 'members'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'book_id' => 'required|exists:books,id',
            'borrow_date' => 'required|date',
            'due_date' => 'required|date|after:borrow_date',
            'book_condition_borrow' => 'required|in:baru,baik,rusak',
            'notes' => 'nullable|string',
        ]);

        // Check if book is available
        $book = Book::findOrFail($validated['book_id']);
        if (!$book->isAvailable()) {
            return back()->with('error', 'Buku tidak tersedia untuk dipinjam.');
        }

        // Check if user has overdue books
        $user = User::findOrFail($validated['user_id']);
        $overdueCount = $user->transactions()
            ->where('status', 'dipinjam')
            ->where('due_date', '<', Carbon::now())
            ->count();

        if ($overdueCount > 0) {
            return back()->with('error', 'Anggota memiliki buku yang terlambat dikembalikan.');
        }

        // Check borrowing limit
        $borrowLimit = Setting::getValue('borrow_limit', 3);
        $currentBorrows = $user->transactions()->where('status', 'dipinjam')->count();
        
        if ($currentBorrows >= $borrowLimit) {
            return back()->with('error', "Anggota sudah mencapai batas maksimal peminjaman ({$borrowLimit} buku).");
        }

        $validated['transaction_code'] = Transaction::generateTransactionCode();
        $validated['staff_id'] = auth()->id();
        $validated['status'] = 'dipinjam';

        $transaction = Transaction::create($validated);

        // Update book stock
        $book->decrement('available_stock');

        ActivityLog::log('Memproses peminjaman buku', $transaction, [], $validated);

        return redirect()->route('transactions.index')->with('success', 'Peminjaman berhasil diproses.');
    }

    public function show(Transaction $transaction)
    {
        $transaction->load(['user', 'book', 'staff', 'fine']);
        return view('transactions.show', compact('transaction'));
    }

    public function return(Transaction $transaction)
    {
        if ($transaction->status !== 'dipinjam') {
            return back()->with('error', 'Transaksi ini sudah diproses sebelumnya.');
        }

        return view('transactions.return', compact('transaction'));
    }

    public function processReturn(Request $request, Transaction $transaction)
    {
        $validated = $request->validate([
            'return_date' => 'required|date',
            'book_condition_return' => 'required|in:baru,baik,rusak',
            'notes' => 'nullable|string',
        ]);

        if ($transaction->status !== 'dipinjam') {
            return back()->with('error', 'Transaksi ini sudah diproses sebelumnya.');
        }

        $oldValues = $transaction->toArray();

        $validated['status'] = 'dikembalikan';
        $transaction->update($validated);

        // Update book stock
        $transaction->book->increment('available_stock');

        // Check if return is late and create fine
        $returnDate = Carbon::parse($validated['return_date']);
        if ($returnDate->gt($transaction->due_date)) {
            $this->createFine($transaction, $returnDate);
        }

        ActivityLog::log('Memproses pengembalian buku', $transaction, $oldValues, $validated);

        return redirect()->route('transactions.index')->with('success', 'Pengembalian berhasil diproses.');
    }

    private function createFine(Transaction $transaction, Carbon $returnDate): void
    {
        $daysLate = $returnDate->diffInDays($transaction->due_date);
        $dailyRate = Setting::getValue('fine_daily_rate', 1000);
        $amount = $daysLate * $dailyRate;

        Fine::create([
            'transaction_id' => $transaction->id,
            'user_id' => $transaction->user_id,
            'amount' => $amount,
            'days_late' => $daysLate,
            'daily_rate' => $dailyRate,
            'reason' => 'terlambat',
            'status' => 'belum_bayar',
        ]);
    }

    public function extend(Request $request, Transaction $transaction)
    {
        $validated = $request->validate([
            'new_due_date' => 'required|date|after:' . $transaction->due_date,
        ]);

        if ($transaction->status !== 'dipinjam') {
            return back()->with('error', 'Transaksi ini tidak dapat diperpanjang.');
        }

        $oldValues = $transaction->toArray();
        $transaction->update(['due_date' => $validated['new_due_date']]);

        ActivityLog::log('Memperpanjang peminjaman buku', $transaction, $oldValues, $validated);

        return back()->with('success', 'Peminjaman berhasil diperpanjang.');
    }

    public function myTransactions()
    {
        $user = auth()->user();

        $transactions = $user->transactions()
            ->with(['book.category', 'staff'])
            ->when(request('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->latest()
            ->paginate(20);

        return view('transactions.my-transactions', compact('transactions'));
    }
}
