<x-layouts.app :title="__('Daftar Buku')">
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Daftar Buku</h1>
                <p class="text-gray-600 dark:text-gray-400"><PERSON><PERSON><PERSON> kole<PERSON> buku perpus<PERSON>an</p>
            </div>
            
            @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
                <flux:button href="{{ route('books.create') }}" variant="primary">
                    <flux:icon.plus class="w-4 h-4 mr-2" />
                    Tambah Buku
                </flux:button>
            @endif
        </div>

        <!-- Book Search Component -->
        <livewire:book-search />
    </div>
</x-layouts.app>
