<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Fine extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'user_id',
        'amount',
        'days_late',
        'daily_rate',
        'reason',
        'status',
        'paid_date',
        'paid_by',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'daily_rate' => 'decimal:2',
            'days_late' => 'integer',
            'paid_date' => 'date',
        ];
    }

    /**
     * Get the transaction that owns the fine
     */
    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the user that owns the fine
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the staff who processed the payment
     */
    public function paidByStaff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'paid_by');
    }

    /**
     * Check if fine is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'sudah_bayar';
    }

    /**
     * Check if fine is waived
     */
    public function isWaived(): bool
    {
        return $this->status === 'dibebaskan';
    }

    /**
     * Mark fine as paid
     */
    public function markAsPaid(int $paidBy): void
    {
        $this->update([
            'status' => 'sudah_bayar',
            'paid_date' => now(),
            'paid_by' => $paidBy,
        ]);
    }

    /**
     * Mark fine as waived
     */
    public function markAsWaived(int $waivedBy): void
    {
        $this->update([
            'status' => 'dibebaskan',
            'paid_date' => now(),
            'paid_by' => $waivedBy,
        ]);
    }
}
