<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Fiksi', 'code' => 'FIK', 'description' => 'Buku-buku fiksi dan novel'],
            ['name' => 'Non-Fiksi', 'code' => 'NFK', 'description' => 'Buku-buku non-fiksi'],
            ['name' => 'Pendidikan', 'code' => 'EDU', 'description' => 'Buku-buku pendidikan dan pembelajaran'],
            ['name' => 'Sains', 'code' => 'SCI', 'description' => 'Buku-buku sains dan teknologi'],
            ['name' => 'Sejarah', 'code' => 'HIS', 'description' => 'Buku-buku sejarah'],
            ['name' => 'Agama', 'code' => 'REL', 'description' => 'Buku-buku agama dan spiritual'],
            ['name' => 'Biografi', 'code' => 'BIO', 'description' => 'Buku-buku biografi dan autobiografi'],
            ['name' => 'Referensi', 'code' => 'REF', 'description' => 'Buku-buku referensi dan ensiklopedia'],
            ['name' => 'Komputer', 'code' => 'COM', 'description' => 'Buku-buku teknologi dan komputer'],
            ['name' => 'Bahasa', 'code' => 'LAN', 'description' => 'Buku-buku pembelajaran bahasa'],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
