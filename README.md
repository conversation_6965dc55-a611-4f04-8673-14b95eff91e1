# Sistem Manajemen Perpustakaan

Aplikasi web untuk mengelola perpustakaan sekolah yang dibangun dengan Laravel dan Livewire.

## 🚀 Fitur Utama

### 👥 Manajemen Pengguna
- **Role-based Access Control**: Admin, Staff, dan <PERSON>swa dengan hak akses berbeda
- **Registrasi dan Autentikasi**: Sistem login yang aman dengan validasi
- **Profil Pengguna**: Manajemen data pribadi dan informasi akademik

### 📚 Manajemen Buku
- **CRUD Buku**: Tambah, edit, hapus, dan lihat detail buku
- **Kategorisasi**: Sistem kategori buku yang terorganisir
- **Barcode Generation**: Generate barcode otomatis untuk setiap buku
- **Pencarian Advanced**: Cari berdasarkan judul, penulis, ISBN, atau barcode
- **Upload Cover**: Upload gambar cover buku

### 🔄 Manajemen Transaksi
- **Peminjaman Buku**: Proses peminjaman dengan validasi kelengkapan
- **Pengembalian Buku**: Proses pengembalian dengan pengecekan kondisi
- **Perpanjangan**: Sistem perpanjangan peminjaman
- **Riwayat Transaksi**: Tracking lengkap semua aktivitas peminjaman

### 💰 Sistem Denda
- **Kalkulasi Otomatis**: Hitung denda berdasarkan hari keterlambatan
- **Manajemen Pembayaran**: Proses pembayaran dan pembebasan denda
- **Laporan Denda**: Tracking denda yang belum dibayar

### 🔔 Sistem Notifikasi
- **Multi-channel**: Email, WhatsApp, SMS, dan notifikasi sistem
- **Pengingat Otomatis**: Reminder sebelum jatuh tempo
- **Notifikasi Keterlambatan**: Alert untuk buku yang terlambat
- **Konfirmasi Transaksi**: Notifikasi sukses peminjaman/pengembalian

### 📊 Laporan dan Analytics
- **Dashboard Interaktif**: Statistik real-time aktivitas perpustakaan
- **Laporan Komprehensif**: Laporan buku, transaksi, anggota, dan denda
- **Export Data**: Export ke PDF dan Excel
- **Analytics**: Analisis buku populer dan aktivitas anggota

### ⚙️ Konfigurasi Sistem
- **Pengaturan Fleksibel**: Konfigurasi batas peminjaman, denda, dan notifikasi
- **Backup Otomatis**: Sistem backup database terjadwal
- **Activity Logging**: Log semua aktivitas pengguna untuk audit

## 🛠️ Teknologi yang Digunakan

- **Backend**: Laravel 12.x
- **Frontend**: Livewire, Flux UI Components
- **Database**: MySQL
- **Styling**: Tailwind CSS
- **Barcode**: Picqer Barcode Generator
- **PDF**: DomPDF
- **Excel**: Maatwebsite Excel
- **Image Processing**: Intervention Image

## 📋 Persyaratan Sistem

- PHP >= 8.2
- Composer
- Node.js & NPM
- MySQL >= 8.0
- Web Server (Apache/Nginx)

## 🚀 Instalasi

### 1. Clone Repository
```bash
git clone https://github.com/username/perpustakaan.git
cd perpustakaan
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Konfigurasi Environment
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Konfigurasi Database
Edit file `.env` dan sesuaikan konfigurasi database:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=perpustakaan
DB_USERNAME=root
DB_PASSWORD=
```

### 5. Migrasi dan Seeding
```bash
# Run migrations
php artisan migrate

# Seed database dengan data awal
php artisan db:seed
```

### 6. Build Assets
```bash
# Build frontend assets
npm run build

# Atau untuk development
npm run dev
```

### 7. Storage Link
```bash
# Create storage link untuk file uploads
php artisan storage:link
```

### 8. Jalankan Aplikasi
```bash
# Start development server
php artisan serve
```

Aplikasi akan berjalan di `http://localhost:8000`

## 👤 Akun Default

Setelah seeding, Anda dapat login dengan akun berikut:

### Admin
- **Email**: <EMAIL>
- **Password**: password

### Staff
- **Email**: <EMAIL>
- **Password**: password

### Siswa
- **Email**: <EMAIL>
- **Password**: password

## 📁 Struktur Proyek

```
perpustakaan/
├── app/
│   ├── Console/Commands/          # Artisan commands
│   ├── Http/
│   │   ├── Controllers/           # Controllers
│   │   └── Middleware/            # Custom middleware
│   ├── Livewire/                  # Livewire components
│   ├── Models/                    # Eloquent models
│   └── Services/                  # Service classes
├── database/
│   ├── migrations/                # Database migrations
│   └── seeders/                   # Database seeders
├── resources/
│   ├── views/                     # Blade templates
│   ├── css/                       # Stylesheets
│   └── js/                        # JavaScript files
└── routes/
    └── web.php                    # Web routes
```

## 🔧 Konfigurasi Tambahan

### Notifikasi Email
Konfigurasi SMTP di file `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

### Scheduled Tasks
Tambahkan cron job untuk menjalankan task otomatis:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### Queue Workers (Opsional)
Untuk notifikasi asinkron:
```bash
php artisan queue:work
```

## 📱 Penggunaan

### Untuk Admin/Staff:
1. Login ke sistem
2. Kelola data buku, anggota, dan kategori
3. Proses peminjaman dan pengembalian
4. Monitor denda dan pembayaran
5. Generate laporan

### Untuk Siswa:
1. Login dengan akun siswa
2. Cari dan lihat katalog buku
3. Lihat riwayat peminjaman
4. Cek status denda
5. Update profil

## 🔄 Maintenance

### Backup Database
```bash
php artisan backup:run
```

### Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### Update Dependencies
```bash
composer update
npm update
```

## 🤝 Kontribusi

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Proyek ini menggunakan lisensi MIT. Lihat file `LICENSE` untuk detail.

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan:
- Buat issue di GitHub
- Email: <EMAIL>
- Dokumentasi: [Wiki](https://github.com/username/perpustakaan/wiki)

## 🙏 Acknowledgments

- Laravel Framework
- Livewire
- Flux UI
- Tailwind CSS
- Semua kontributor open source

---

**Dibuat dengan ❤️ untuk pendidikan Indonesia**
