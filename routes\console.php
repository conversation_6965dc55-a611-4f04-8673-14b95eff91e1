<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule library tasks
Schedule::command('library:send-due-reminders')
    ->dailyAt('09:00')
    ->description('Send due date reminders to users');

Schedule::command('library:send-overdue-notifications')
    ->dailyAt('10:00')
    ->description('Send overdue notifications and create fines');

// Optional: Database backup (if backup package is installed)
// Schedule::command('backup:clean')->daily()->at('01:00');
// Schedule::command('backup:run')->daily()->at('02:00');
