<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Setting;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send notification to user
     */
    public function send(User $user, string $title, string $message, string $type = 'general', array $metadata = []): void
    {
        // Create system notification
        $notification = Notification::create([
            'user_id' => $user->id,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'channel' => 'system',
            'metadata' => $metadata,
        ]);

        // Send via enabled channels
        if (Setting::getValue('notification_email', true) && $user->email) {
            $this->sendEmail($user, $title, $message, $notification);
        }

        if (Setting::getValue('notification_whatsapp', false) && $user->phone) {
            $this->sendWhatsApp($user, $title, $message, $notification);
        }

        if (Setting::getValue('notification_sms', false) && $user->phone) {
            $this->sendSMS($user, $title, $message, $notification);
        }
    }

    /**
     * Send due date reminder
     */
    public function sendDueDateReminder(Transaction $transaction): void
    {
        $daysRemaining = now()->diffInDays($transaction->due_date, false);
        
        $title = 'Pengingat Jatuh Tempo Peminjaman';
        $message = "Buku '{$transaction->book->title}' akan jatuh tempo dalam {$daysRemaining} hari (tanggal {$transaction->due_date->format('d/m/Y')}). Harap segera dikembalikan.";
        
        $this->send(
            $transaction->user,
            $title,
            $message,
            'reminder',
            [
                'transaction_id' => $transaction->id,
                'book_id' => $transaction->book_id,
                'due_date' => $transaction->due_date->toDateString(),
            ]
        );
    }

    /**
     * Send overdue notification
     */
    public function sendOverdueNotification(Transaction $transaction): void
    {
        $daysLate = $transaction->daysLate();
        
        $title = 'Buku Terlambat Dikembalikan';
        $message = "Buku '{$transaction->book->title}' sudah terlambat {$daysLate} hari dari tanggal jatuh tempo ({$transaction->due_date->format('d/m/Y')}). Segera kembalikan untuk menghindari denda tambahan.";
        
        $this->send(
            $transaction->user,
            $title,
            $message,
            'overdue',
            [
                'transaction_id' => $transaction->id,
                'book_id' => $transaction->book_id,
                'days_late' => $daysLate,
                'due_date' => $transaction->due_date->toDateString(),
            ]
        );
    }

    /**
     * Send return success notification
     */
    public function sendReturnSuccessNotification(Transaction $transaction): void
    {
        $title = 'Pengembalian Buku Berhasil';
        $message = "Terima kasih! Buku '{$transaction->book->title}' telah berhasil dikembalikan pada tanggal {$transaction->return_date->format('d/m/Y')}.";
        
        $this->send(
            $transaction->user,
            $title,
            $message,
            'return_success',
            [
                'transaction_id' => $transaction->id,
                'book_id' => $transaction->book_id,
                'return_date' => $transaction->return_date->toDateString(),
            ]
        );
    }

    /**
     * Send fine payment notification
     */
    public function sendFinePaymentNotification(User $user, float $amount): void
    {
        $title = 'Pembayaran Denda Berhasil';
        $message = "Pembayaran denda sebesar Rp " . number_format($amount, 0, ',', '.') . " telah berhasil diproses. Terima kasih.";
        
        $this->send(
            $user,
            $title,
            $message,
            'fine_payment',
            [
                'amount' => $amount,
                'payment_date' => now()->toDateString(),
            ]
        );
    }

    /**
     * Send email notification
     */
    private function sendEmail(User $user, string $title, string $message, Notification $notification): void
    {
        try {
            // Create email notification record
            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => $notification->type,
                'channel' => 'email',
                'metadata' => $notification->metadata,
            ]);

            // TODO: Implement actual email sending
            // Mail::to($user->email)->send(new LibraryNotificationMail($title, $message));
            
            Log::info("Email notification sent to {$user->email}: {$title}");
        } catch (\Exception $e) {
            Log::error("Failed to send email notification: " . $e->getMessage());
        }
    }

    /**
     * Send WhatsApp notification
     */
    private function sendWhatsApp(User $user, string $title, string $message, Notification $notification): void
    {
        try {
            // Create WhatsApp notification record
            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => $notification->type,
                'channel' => 'whatsapp',
                'metadata' => $notification->metadata,
            ]);

            // TODO: Implement WhatsApp API integration
            Log::info("WhatsApp notification sent to {$user->phone}: {$title}");
        } catch (\Exception $e) {
            Log::error("Failed to send WhatsApp notification: " . $e->getMessage());
        }
    }

    /**
     * Send SMS notification
     */
    private function sendSMS(User $user, string $title, string $message, Notification $notification): void
    {
        try {
            // Create SMS notification record
            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => $notification->type,
                'channel' => 'sms',
                'metadata' => $notification->metadata,
            ]);

            // TODO: Implement SMS API integration
            Log::info("SMS notification sent to {$user->phone}: {$title}");
        } catch (\Exception $e) {
            Log::error("Failed to send SMS notification: " . $e->getMessage());
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId): void
    {
        Notification::where('id', $notificationId)->update(['is_read' => true]);
    }

    /**
     * Mark all user notifications as read
     */
    public function markAllAsRead(int $userId): void
    {
        Notification::where('user_id', $userId)->update(['is_read' => true]);
    }

    /**
     * Get unread notifications count for user
     */
    public function getUnreadCount(int $userId): int
    {
        return Notification::where('user_id', $userId)
            ->where('is_read', false)
            ->count();
    }
}
