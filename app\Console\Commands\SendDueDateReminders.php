<?php

namespace App\Console\Commands;

use App\Models\Transaction;
use App\Models\Setting;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SendDueDateReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'library:send-due-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send due date reminders to users with books due soon';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService): int
    {
        $reminderDays = Setting::getValue('reminder_days_before', 2);
        $targetDate = Carbon::now()->addDays($reminderDays)->toDateString();

        $transactions = Transaction::with(['user', 'book'])
            ->where('status', 'dipinjam')
            ->whereDate('due_date', $targetDate)
            ->get();

        $count = 0;
        foreach ($transactions as $transaction) {
            try {
                $notificationService->sendDueDateReminder($transaction);
                $count++;
                $this->info("Reminder sent to {$transaction->user->name} for book: {$transaction->book->title}");
            } catch (\Exception $e) {
                $this->error("Failed to send reminder to {$transaction->user->name}: " . $e->getMessage());
            }
        }

        $this->info("Total reminders sent: {$count}");
        return Command::SUCCESS;
    }
}
