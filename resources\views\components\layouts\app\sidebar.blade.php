<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <!-- Simplified Sidebar for Testing -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700 transform transition-transform duration-300 ease-in-out -translate-x-full lg:translate-x-0">
            <!-- Logo -->
            <div class="flex items-center justify-between p-4 border-b border-zinc-200 dark:border-zinc-700">
                <a href="{{ route('dashboard') }}" class="flex items-center space-x-2" wire:navigate>
                    <x-app-logo />
                </a>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-6 overflow-y-auto">
                <!-- Dashboard -->
                <div>
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Dashboard</h3>
                    <div class="mt-2 space-y-1">
                        <a href="{{ route('dashboard') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                            <span class="mr-3">🏠</span>
                            Dashboard
                        </a>
                        <a href="{{ route('debug') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('debug') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                            <span class="mr-3">🔧</span>
                            Debug Info
                        </a>
                        <a href="{{ route('test-sidebar') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('test-sidebar') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                            <span class="mr-3">🧪</span>
                            Test Sidebar
                        </a>
                    </div>
                </div>

                <!-- Katalog -->
                <div>
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Katalog</h3>
                    <div class="mt-2 space-y-1">
                        <a href="{{ route('books.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('books.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                            <span class="mr-3">📚</span>
                            Daftar Buku
                        </a>

                        @if(auth()->user()->isAdmin())
                            <a href="{{ route('categories.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('categories.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">🏷️</span>
                                Kategori
                            </a>
                        @endif
                    </div>
                </div>

                @if(auth()->user()->isAdmin() || auth()->user()->isStaff())
                    <!-- Transaksi -->
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Transaksi</h3>
                        <div class="mt-2 space-y-1">
                            <a href="{{ route('transactions.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('transactions.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">🔄</span>
                                Peminjaman
                            </a>
                            <a href="{{ route('fines.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('fines.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">💰</span>
                                Denda
                            </a>
                        </div>
                    </div>

                    <!-- Manajemen -->
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Manajemen</h3>
                        <div class="mt-2 space-y-1">
                            <a href="{{ route('users.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('users.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">👥</span>
                                Anggota
                            </a>
                            <a href="{{ route('reports.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('reports.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">📊</span>
                                Laporan
                            </a>
                        </div>
                    </div>

                    @if(auth()->user()->isAdmin())
                        <!-- Sistem -->
                        <div>
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Sistem</h3>
                            <div class="mt-2 space-y-1">
                                <a href="{{ route('system-settings.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('system-settings.*') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                    <span class="mr-3">⚙️</span>
                                    Pengaturan
                                </a>
                            </div>
                        </div>
                    @endif
                @else
                    <!-- Aktivitas Siswa -->
                    <div>
                        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Aktivitas Saya</h3>
                        <div class="mt-2 space-y-1">
                            <a href="{{ route('my-transactions') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('my-transactions') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">📖</span>
                                Riwayat Pinjam
                            </a>
                            <a href="{{ route('my-fines') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs('my-fines') ? 'bg-zinc-200 dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800' }}" wire:navigate>
                                <span class="mr-3">💸</span>
                                Denda Saya
                            </a>
                        </div>
                    </div>
                @endif
            </nav>

            <!-- User Menu -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-900">
                <div class="flex items-center space-x-3 mb-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-zinc-200 dark:bg-zinc-700 rounded-full flex items-center justify-center text-sm font-medium text-zinc-900 dark:text-zinc-100">
                            {{ auth()->user()->initials() }}
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-zinc-900 dark:text-zinc-100 truncate">
                            {{ auth()->user()->name }}
                        </p>
                        <p class="text-xs text-zinc-500 dark:text-zinc-400 truncate">
                            {{ auth()->user()->email }}
                        </p>
                    </div>
                </div>

                <div class="space-y-1">
                    <a href="{{ route('settings.profile') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800" wire:navigate>
                        <span class="mr-3">⚙️</span>
                        Settings
                    </a>

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <button type="submit" class="group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20">
                            <span class="mr-3">🚪</span>
                            Log Out
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Mobile Header -->
        <div class="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white dark:bg-zinc-800 border-b border-zinc-200 dark:border-zinc-700">
            <div class="flex items-center justify-between px-4 py-3">
                <button type="button" class="text-zinc-500 hover:text-zinc-600 dark:text-zinc-400 dark:hover:text-zinc-300" onclick="toggleMobileSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-zinc-200 dark:bg-zinc-700 rounded-full flex items-center justify-center text-sm font-medium text-zinc-900 dark:text-zinc-100">
                        {{ auth()->user()->initials() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="lg:ml-64 min-h-screen bg-white dark:bg-zinc-800">
            <div class="lg:hidden h-16"></div> <!-- Spacer for mobile header -->
            {{ $slot }}
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div id="mobile-sidebar-overlay" class="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden hidden" onclick="toggleMobileSidebar()"></div>

        <script>
            function toggleMobileSidebar() {
                const sidebar = document.querySelector('.fixed.inset-y-0.left-0.z-50');
                const overlay = document.getElementById('mobile-sidebar-overlay');

                if (sidebar.classList.contains('-translate-x-full')) {
                    sidebar.classList.remove('-translate-x-full');
                    overlay.classList.remove('hidden');
                } else {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                }
            }

            // Close sidebar on mobile when clicking a link
            document.addEventListener('DOMContentLoaded', function() {
                const sidebarLinks = document.querySelectorAll('.fixed.inset-y-0.left-0.z-50 a');
                sidebarLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth < 1024) {
                            toggleMobileSidebar();
                        }
                    });
                });
            });
        </script>

        @fluxScripts
    </body>
</html>
