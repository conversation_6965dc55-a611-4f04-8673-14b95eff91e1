<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_code',
        'user_id',
        'book_id',
        'staff_id',
        'borrow_date',
        'due_date',
        'return_date',
        'status',
        'book_condition_borrow',
        'book_condition_return',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'borrow_date' => 'date',
            'due_date' => 'date',
            'return_date' => 'date',
        ];
    }

    /**
     * Get the user (borrower) that owns the transaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book that owns the transaction
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the staff that processed the transaction
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'staff_id');
    }

    /**
     * Get the fine associated with the transaction
     */
    public function fine(): HasOne
    {
        return $this->hasOne(Fine::class);
    }

    /**
     * Check if transaction is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'dipinjam' && Carbon::now()->gt($this->due_date);
    }

    /**
     * Get days late
     */
    public function daysLate(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return Carbon::now()->diffInDays($this->due_date);
    }

    /**
     * Calculate fine amount
     */
    public function calculateFine(): float
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $dailyRate = Setting::getValue('fine_daily_rate', 1000);
        return $this->daysLate() * $dailyRate;
    }

    /**
     * Generate unique transaction code
     */
    public static function generateTransactionCode(): string
    {
        $prefix = 'TRX';
        $date = Carbon::now()->format('Ymd');
        $sequence = str_pad(static::whereDate('created_at', Carbon::today())->count() + 1, 4, '0', STR_PAD_LEFT);
        
        return $prefix . $date . $sequence;
    }
}
