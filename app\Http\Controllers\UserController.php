<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function index()
    {
        $users = User::when(request('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('student_id', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            })
            ->when(request('role'), function ($query, $role) {
                $query->where('role', $role);
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->when(request('class'), function ($query, $class) {
                $query->where('class', $class);
            })
            ->latest()
            ->paginate(20);

        $classes = User::where('role', 'siswa')
            ->whereNotNull('class')
            ->distinct()
            ->pluck('class')
            ->sort();

        return view('users.index', compact('users', 'classes'));
    }

    public function create()
    {
        return view('users.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,staff,siswa',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:L,P',
            'student_id' => [
                'nullable',
                'string',
                'max:20',
                Rule::requiredIf($request->role === 'siswa'),
                'unique:users,student_id'
            ],
            'class' => [
                'nullable',
                'string',
                'max:50',
                Rule::requiredIf($request->role === 'siswa')
            ],
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_active'] = true;

        $user = User::create($validated);

        ActivityLog::log('Menambah pengguna baru', $user, [], $validated);

        return redirect()->route('users.index')->with('success', 'Pengguna berhasil ditambahkan.');
    }

    public function show(User $user)
    {
        $user->load(['transactions.book', 'fines.transaction.book']);
        
        $stats = [
            'total_borrowed' => $user->transactions()->count(),
            'currently_borrowed' => $user->transactions()->where('status', 'dipinjam')->count(),
            'total_returned' => $user->transactions()->where('status', 'dikembalikan')->count(),
            'overdue_books' => $user->transactions()
                ->where('status', 'dipinjam')
                ->where('due_date', '<', now())
                ->count(),
            'total_fines' => $user->fines()->sum('amount'),
            'unpaid_fines' => $user->fines()->where('status', 'belum_bayar')->sum('amount'),
        ];

        $recentTransactions = $user->transactions()
            ->with(['book'])
            ->latest()
            ->take(10)
            ->get();

        $currentBorrows = $user->transactions()
            ->with(['book'])
            ->where('status', 'dipinjam')
            ->get();

        $unpaidFines = $user->fines()
            ->with(['transaction.book'])
            ->where('status', 'belum_bayar')
            ->get();

        return view('users.show', compact('user', 'stats', 'recentTransactions', 'currentBorrows', 'unpaidFines'));
    }

    public function edit(User $user)
    {
        return view('users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,staff,siswa',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:L,P',
            'student_id' => [
                'nullable',
                'string',
                'max:20',
                Rule::requiredIf($request->role === 'siswa'),
                'unique:users,student_id,' . $user->id
            ],
            'class' => [
                'nullable',
                'string',
                'max:50',
                Rule::requiredIf($request->role === 'siswa')
            ],
            'is_active' => 'boolean',
        ]);

        $oldValues = $user->toArray();

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        ActivityLog::log('Mengubah data pengguna', $user, $oldValues, $validated);

        return redirect()->route('users.index')->with('success', 'Data pengguna berhasil diperbarui.');
    }

    public function destroy(User $user)
    {
        // Check if user has active transactions
        if ($user->transactions()->where('status', 'dipinjam')->exists()) {
            return back()->with('error', 'Tidak dapat menghapus pengguna yang memiliki peminjaman aktif.');
        }

        // Check if user has unpaid fines
        if ($user->fines()->where('status', 'belum_bayar')->exists()) {
            return back()->with('error', 'Tidak dapat menghapus pengguna yang memiliki denda yang belum dibayar.');
        }

        $oldValues = $user->toArray();
        $user->delete();

        ActivityLog::log('Menghapus pengguna', null, $oldValues, []);

        return redirect()->route('users.index')->with('success', 'Pengguna berhasil dihapus.');
    }

    public function toggleStatus(User $user)
    {
        $oldValues = $user->toArray();
        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'diaktifkan' : 'dinonaktifkan';
        ActivityLog::log("Status pengguna {$status}", $user, $oldValues, ['is_active' => $user->is_active]);

        return back()->with('success', "Status pengguna berhasil {$status}.");
    }
}
