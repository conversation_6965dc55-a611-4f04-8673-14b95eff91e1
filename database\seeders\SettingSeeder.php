<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            ['key' => 'library_name', 'value' => 'Perpustakaan SMA Negeri 1', 'type' => 'string', 'group' => 'general', 'description' => 'Nama perpustakaan'],
            ['key' => 'library_address', 'value' => 'Jl. Pendidikan No. 123, Jakarta', 'type' => 'string', 'group' => 'general', 'description' => 'Alamat perpustakaan'],
            ['key' => 'library_phone', 'value' => '021-1234567', 'type' => 'string', 'group' => 'general', 'description' => 'Nomor telepon perpustakaan'],
            ['key' => 'library_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'general', 'description' => 'Email perpustakaan'],
            ['key' => 'library_website', 'value' => 'https://perpus.sman1.sch.id', 'type' => 'string', 'group' => 'general', 'description' => 'Website perpustakaan'],
            
            // Transaction Settings
            ['key' => 'borrow_limit', 'value' => '3', 'type' => 'integer', 'group' => 'transaction', 'description' => 'Batas maksimal peminjaman per anggota'],
            ['key' => 'default_loan_days', 'value' => '7', 'type' => 'integer', 'group' => 'transaction', 'description' => 'Lama peminjaman default (hari)'],
            ['key' => 'max_extend_days', 'value' => '7', 'type' => 'integer', 'group' => 'transaction', 'description' => 'Maksimal perpanjangan (hari)'],
            ['key' => 'max_extend_times', 'value' => '2', 'type' => 'integer', 'group' => 'transaction', 'description' => 'Maksimal jumlah perpanjangan'],
            
            // Fine Settings
            ['key' => 'fine_daily_rate', 'value' => '1000', 'type' => 'integer', 'group' => 'fine', 'description' => 'Denda per hari (Rupiah)'],
            ['key' => 'fine_damage_rate', 'value' => '50000', 'type' => 'integer', 'group' => 'fine', 'description' => 'Denda kerusakan buku (Rupiah)'],
            ['key' => 'fine_lost_rate', 'value' => '100000', 'type' => 'integer', 'group' => 'fine', 'description' => 'Denda kehilangan buku (Rupiah)'],
            
            // Notification Settings
            ['key' => 'notification_email', 'value' => '1', 'type' => 'boolean', 'group' => 'notification', 'description' => 'Aktifkan notifikasi email'],
            ['key' => 'notification_whatsapp', 'value' => '0', 'type' => 'boolean', 'group' => 'notification', 'description' => 'Aktifkan notifikasi WhatsApp'],
            ['key' => 'notification_sms', 'value' => '0', 'type' => 'boolean', 'group' => 'notification', 'description' => 'Aktifkan notifikasi SMS'],
            ['key' => 'reminder_days_before', 'value' => '2', 'type' => 'integer', 'group' => 'notification', 'description' => 'Pengingat berapa hari sebelum jatuh tempo'],
            ['key' => 'overdue_reminder_interval', 'value' => '3', 'type' => 'integer', 'group' => 'notification', 'description' => 'Interval pengingat keterlambatan (hari)'],
            
            // System Settings
            ['key' => 'backup_enabled', 'value' => '1', 'type' => 'boolean', 'group' => 'system', 'description' => 'Aktifkan backup otomatis'],
            ['key' => 'backup_interval', 'value' => '7', 'type' => 'integer', 'group' => 'system', 'description' => 'Interval backup (hari)'],
            ['key' => 'maintenance_mode', 'value' => '0', 'type' => 'boolean', 'group' => 'system', 'description' => 'Mode maintenance'],
            ['key' => 'max_file_upload_size', 'value' => '2048', 'type' => 'integer', 'group' => 'system', 'description' => 'Maksimal ukuran file upload (KB)'],
            
            // Report Settings
            ['key' => 'report_logo', 'value' => '', 'type' => 'string', 'group' => 'report', 'description' => 'Logo untuk laporan'],
            ['key' => 'report_footer', 'value' => 'Perpustakaan SMA Negeri 1 - Sistem Manajemen Perpustakaan', 'type' => 'string', 'group' => 'report', 'description' => 'Footer laporan'],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
