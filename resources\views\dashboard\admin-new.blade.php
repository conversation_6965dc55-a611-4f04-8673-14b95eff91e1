<x-layouts.app :title="__('Dashboard')">
    <!-- Custom CSS for Tabler-like design -->
    <style>
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e7e9;
            padding: 1.5rem 0;
            margin-bottom: 2rem;
        }
        .page-pretitle {
            color: #6c757d;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.04em;
            margin-bottom: 0.25rem;
        }
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        .card {
            background: #fff;
            border: 1px solid #e6e7e9;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }
        .card-body {
            padding: 1.5rem;
        }
        .card-sm .card-body {
            padding: 1rem;
        }
        .avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .bg-primary { background-color: #3b82f6 !important; }
        .bg-success { background-color: #10b981 !important; }
        .bg-warning { background-color: #f59e0b !important; }
        .bg-danger { background-color: #ef4444 !important; }
        .bg-info { background-color: #06b6d4 !important; }
        .bg-purple { background-color: #8b5cf6 !important; }
        .bg-teal { background-color: #14b8a6 !important; }
        .bg-orange { background-color: #f97316 !important; }
        .text-white { color: #fff !important; }
        .font-weight-medium { font-weight: 500; }
        .text-muted { color: #6c757d; }
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            transition: all 0.15s ease-in-out;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: #fff;
            border-color: #3b82f6;
        }
        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        .btn-success {
            background-color: #10b981;
            color: #fff;
            border-color: #10b981;
        }
        .btn-success:hover {
            background-color: #059669;
            border-color: #059669;
        }
        .btn-outline {
            background-color: transparent;
            color: #6c757d;
            border-color: #e6e7e9;
        }
        .btn-outline:hover {
            background-color: #f8f9fa;
            color: #495057;
        }
        .container-xl {
            max-width: 1320px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -0.75rem;
        }
        .col, .col-auto, .col-sm-6, .col-lg-3 {
            padding: 0 0.75rem;
        }
        .col-auto {
            flex: 0 0 auto;
            width: auto;
        }
        .col {
            flex: 1 0 0%;
        }
        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .row-deck {
            margin-bottom: 1.5rem;
        }
        .row-cards .col-sm-6:not(:last-child),
        .row-cards .col-lg-3:not(:last-child) {
            margin-bottom: 1.5rem;
        }
        @media (max-width: 576px) {
            .col-sm-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
        @media (max-width: 992px) {
            .col-lg-3 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }
        .icon {
            width: 1rem;
            height: 1rem;
            margin-right: 0.5rem;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-borrowed {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-returned {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-overdue {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .dark .page-header {
            background: #1f2937;
            border-color: #374151;
        }
        .dark .page-title {
            color: #f9fafb;
        }
        .dark .card {
            background: #1f2937;
            border-color: #374151;
        }
        .dark .text-muted {
            color: #9ca3af;
        }
    </style>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container-xl">
            <div class="row align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Sistem Manajemen Perpustakaan
                    </div>
                    <h2 class="page-title">
                        Dashboard Administrator
                    </h2>
                </div>
                <div class="col-auto">
                    <div style="display: flex; gap: 0.5rem;">
                        <a href="{{ route('books.create') }}" class="btn btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            Tambah Buku
                        </a>
                        <a href="{{ route('transactions.create') }}" class="btn btn-success">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M9 12l2 2l4 -4"/>
                                <circle cx="12" cy="12" r="9"/>
                            </svg>
                            Pinjam Buku
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page Body -->
    <div class="container-xl">
        <!-- Statistics Cards Row -->
        <div class="row row-deck row-cards">
            <!-- Total Books Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-primary text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M3 19a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/>
                                        <path d="M3 6a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/>
                                        <line x1="3" y1="6" x2="3" y2="19"/>
                                        <line x1="12" y1="6" x2="12" y2="19"/>
                                        <line x1="21" y1="6" x2="21" y2="19"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['total_books']) }}
                                </div>
                                <div class="text-muted">
                                    Total Buku
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Members Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-success text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <circle cx="9" cy="7" r="4"/>
                                        <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                        <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['total_members']) }}
                                </div>
                                <div class="text-muted">
                                    Total Anggota
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Books Borrowed Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-warning text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <circle cx="12" cy="12" r="9"/>
                                        <polyline points="12,7 12,12 15,15"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['books_borrowed']) }}
                                </div>
                                <div class="text-muted">
                                    Sedang Dipinjam
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Books Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-danger text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M12 9v2l1.5 1.5"/>
                                        <circle cx="12" cy="12" r="9"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['books_overdue']) }}
                                </div>
                                <div class="text-muted">
                                    Buku Terlambat
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Second Row of Statistics -->
        <div class="row row-deck row-cards">
            <!-- Total Fines Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-purple text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M17 8v-3a1 1 0 0 0 -1 -1h-10a2 2 0 0 0 0 4h12a1 1 0 0 1 1 1v3m0 4v3a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12"/>
                                        <path d="M20 12v4h-4a2 2 0 0 1 0 -4h4"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    Rp {{ number_format($stats['total_fines'], 0, ',', '.') }}
                                </div>
                                <div class="text-muted">
                                    Total Denda
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Books Returned Today Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-info text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M9 12l2 2l4 -4"/>
                                        <circle cx="12" cy="12" r="9"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['books_returned_today']) }}
                                </div>
                                <div class="text-muted">
                                    Dikembalikan Hari Ini
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Staff Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-teal text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <circle cx="9" cy="7" r="4"/>
                                        <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                        <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['total_staff']) }}
                                </div>
                                <div class="text-muted">
                                    Total Staff
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Categories Card -->
            <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <span class="bg-orange text-white avatar">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M9 4h3l2 2h5a2 2 0 0 1 2 2v7a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-9a2 2 0 0 1 2 -2"/>
                                    </svg>
                                </span>
                            </div>
                            <div class="col">
                                <div class="font-weight-medium" style="font-size: 1.5rem;">
                                    {{ number_format($stats['total_categories']) }}
                                </div>
                                <div class="text-muted">
                                    Total Kategori
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="row">
            <!-- Recent Transactions -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h3 style="margin: 0; font-size: 1.125rem; font-weight: 600;">Transaksi Terbaru</h3>
                            <a href="{{ route('transactions.index') }}" class="btn btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                Lihat Semua
                            </a>
                        </div>
                        <div style="space-y: 1rem;">
                            @forelse($recentTransactions as $transaction)
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid #e6e7e9;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="width: 0.5rem; height: 0.5rem; background-color: #3b82f6; border-radius: 50%; margin-right: 0.75rem;"></div>
                                        <div>
                                            <p style="margin: 0; font-size: 0.875rem; font-weight: 500;">{{ $transaction->user->name }}</p>
                                            <p style="margin: 0; font-size: 0.75rem; color: #6c757d;">{{ $transaction->book->title }}</p>
                                        </div>
                                    </div>
                                    <div style="text-align: right;">
                                        <p style="margin: 0; font-size: 0.75rem; color: #6c757d;">{{ $transaction->created_at->diffForHumans() }}</p>
                                        <span class="status-badge
                                            @if($transaction->status === 'dipinjam') status-borrowed
                                            @elseif($transaction->status === 'dikembalikan') status-returned
                                            @else status-overdue @endif">
                                            {{ ucfirst($transaction->status) }}
                                        </span>
                                    </div>
                                </div>
                            @empty
                                <p style="text-align: center; color: #6c757d; padding: 2rem 0;">Belum ada transaksi</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overdue Books -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h3 style="margin: 0; font-size: 1.125rem; font-weight: 600;">Buku Terlambat</h3>
                            <a href="{{ route('transactions.index', ['status' => 'terlambat']) }}" class="btn btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                Lihat Semua
                            </a>
                        </div>
                        <div style="space-y: 1rem;">
                            @forelse($overdueTransactions as $transaction)
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid #e6e7e9;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="width: 0.5rem; height: 0.5rem; background-color: #ef4444; border-radius: 50%; margin-right: 0.75rem;"></div>
                                        <div>
                                            <p style="margin: 0; font-size: 0.875rem; font-weight: 500;">{{ $transaction->user->name }}</p>
                                            <p style="margin: 0; font-size: 0.75rem; color: #6c757d;">{{ $transaction->book->title }}</p>
                                        </div>
                                    </div>
                                    <div style="text-align: right;">
                                        <p style="margin: 0; font-size: 0.75rem; color: #ef4444;">
                                            {{ $transaction->due_date->diffForHumans() }}
                                        </p>
                                        <p style="margin: 0; font-size: 0.75rem; color: #6c757d;">
                                            {{ $transaction->daysLate() }} hari
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <p style="text-align: center; color: #6c757d; padding: 2rem 0;">Tidak ada buku yang terlambat</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-body">
                <h3 style="margin: 0 0 1rem 0; font-size: 1.125rem; font-weight: 600;">Aksi Cepat</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <a href="{{ route('transactions.create') }}" class="btn btn-outline" style="justify-content: flex-start;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <line x1="12" y1="5" x2="12" y2="19"/>
                            <line x1="5" y1="12" x2="19" y2="12"/>
                        </svg>
                        Pinjam Buku
                    </a>
                    <a href="{{ route('books.create') }}" class="btn btn-outline" style="justify-content: flex-start;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M3 19a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/>
                            <path d="M3 6a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/>
                            <line x1="3" y1="6" x2="3" y2="19"/>
                            <line x1="12" y1="6" x2="12" y2="19"/>
                            <line x1="21" y1="6" x2="21" y2="19"/>
                        </svg>
                        Tambah Buku
                    </a>
                    <a href="{{ route('users.create') }}" class="btn btn-outline" style="justify-content: flex-start;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                        </svg>
                        Tambah Anggota
                    </a>
                    <a href="{{ route('reports.index') }}" class="btn btn-outline" style="justify-content: flex-start;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                            <path d="M9 12l2 2l4 -4"/>
                        </svg>
                        Laporan
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-layouts.app>
