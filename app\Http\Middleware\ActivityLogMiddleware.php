<?php

namespace App\Http\Middleware;

use App\Models\ActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ActivityLogMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Log activity for authenticated users
        if (auth()->check() && $this->shouldLog($request)) {
            ActivityLog::log(
                $this->getActionName($request),
                null,
                [],
                $request->all()
            );
        }

        return $response;
    }

    /**
     * Determine if the request should be logged
     */
    private function shouldLog(Request $request): bool
    {
        // Don't log GET requests or asset requests
        if ($request->isMethod('GET') || $request->is('assets/*')) {
            return false;
        }

        // Log POST, PUT, PATCH, DELETE requests
        return in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    /**
     * Get action name from request
     */
    private function getActionName(Request $request): string
    {
        $route = $request->route();
        
        if ($route && $route->getName()) {
            return $route->getName();
        }

        return $request->method() . ' ' . $request->path();
    }
}
