<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\Category;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Picqer\Barcode\BarcodeGeneratorPNG;

class BookController extends Controller
{
    public function index()
    {
        $books = Book::with('category')
            ->when(request('search'), function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                    ->orWhere('author', 'like', "%{$search}%")
                    ->orWhere('isbn', 'like', "%{$search}%")
                    ->orWhere('barcode', 'like', "%{$search}%");
            })
            ->when(request('category'), function ($query, $category) {
                $query->where('category_id', $category);
            })
            ->when(request('condition'), function ($query, $condition) {
                $query->where('condition', $condition);
            })
            ->paginate(20);

        $categories = Category::where('is_active', true)->get();

        return view('books.index', compact('books', 'categories'));
    }

    public function create()
    {
        $categories = Category::where('is_active', true)->get();
        return view('books.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'publisher' => 'required|string|max:255',
            'publication_year' => 'required|integer|min:1900|max:' . date('Y'),
            'isbn' => 'nullable|string|unique:books,isbn',
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'stock' => 'required|integer|min:1',
            'condition' => 'required|in:baru,baik,rusak',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Generate unique barcode
        $validated['barcode'] = $this->generateUniqueBarcode();
        $validated['available_stock'] = $validated['stock'];

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            $validated['cover_image'] = $request->file('cover_image')->store('book-covers', 'public');
        }

        $book = Book::create($validated);

        ActivityLog::log('Menambah buku baru', $book, [], $validated);

        return redirect()->route('books.index')->with('success', 'Buku berhasil ditambahkan.');
    }

    public function show(Book $book)
    {
        $book->load(['category', 'transactions.user']);
        
        $borrowHistory = $book->transactions()
            ->with(['user', 'staff'])
            ->latest()
            ->paginate(10);

        return view('books.show', compact('book', 'borrowHistory'));
    }

    public function edit(Book $book)
    {
        $categories = Category::where('is_active', true)->get();
        return view('books.edit', compact('book', 'categories'));
    }

    public function update(Request $request, Book $book)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'publisher' => 'required|string|max:255',
            'publication_year' => 'required|integer|min:1900|max:' . date('Y'),
            'isbn' => 'nullable|string|unique:books,isbn,' . $book->id,
            'category_id' => 'required|exists:categories,id',
            'description' => 'nullable|string',
            'stock' => 'required|integer|min:1',
            'condition' => 'required|in:baru,baik,rusak,hilang',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'boolean',
        ]);

        $oldValues = $book->toArray();

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            // Delete old image if exists
            if ($book->cover_image) {
                \Storage::disk('public')->delete($book->cover_image);
            }
            $validated['cover_image'] = $request->file('cover_image')->store('book-covers', 'public');
        }

        // Update available stock if stock changed
        if ($validated['stock'] != $book->stock) {
            $borrowed = $book->currentBorrowersCount();
            $validated['available_stock'] = max(0, $validated['stock'] - $borrowed);
        }

        $book->update($validated);

        ActivityLog::log('Mengubah data buku', $book, $oldValues, $validated);

        return redirect()->route('books.index')->with('success', 'Data buku berhasil diperbarui.');
    }

    public function destroy(Book $book)
    {
        // Check if book has active transactions
        if ($book->transactions()->where('status', 'dipinjam')->exists()) {
            return back()->with('error', 'Tidak dapat menghapus buku yang sedang dipinjam.');
        }

        $oldValues = $book->toArray();

        // Delete cover image if exists
        if ($book->cover_image) {
            \Storage::disk('public')->delete($book->cover_image);
        }

        $book->delete();

        ActivityLog::log('Menghapus buku', null, $oldValues, []);

        return redirect()->route('books.index')->with('success', 'Buku berhasil dihapus.');
    }

    public function generateBarcode(Book $book)
    {
        $generator = new BarcodeGeneratorPNG();
        $barcode = $generator->getBarcode($book->barcode, $generator::TYPE_CODE_128);

        return response($barcode)->header('Content-Type', 'image/png');
    }

    private function generateUniqueBarcode(): string
    {
        do {
            $barcode = 'BK' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (Book::where('barcode', $barcode)->exists());

        return $barcode;
    }
}
