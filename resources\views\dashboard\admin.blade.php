<x-layouts.app :title="__('Dashboard')">
    <div class="space-y-6">
        <!-- Header -->
        <div>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
            <p class="text-gray-600 dark:text-gray-400">Selamat datang kembali! Berikut adalah ringkasan aktivitas perpustakaan.</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <flux:icon.book-open class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Buku</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['total_books']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <flux:icon.users class="w-6 h-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Anggota</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['total_members']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <flux:icon.arrow-path class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Sedang Dipinjam</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['books_borrowed']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                        <flux:icon.exclamation-triangle class="w-6 h-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Terlambat</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['books_overdue']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Stats Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                        <flux:icon.currency-dollar class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Denda</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">Rp {{ number_format($stats['total_fines'], 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
                        <flux:icon.check-circle class="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Dikembalikan Hari Ini</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['books_returned_today']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-teal-100 dark:bg-teal-900 rounded-lg">
                        <flux:icon.user-group class="w-6 h-6 text-teal-600 dark:text-teal-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Staff</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['total_staff']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                        <flux:icon.tag class="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Kategori</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ number_format($stats['total_categories']) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Transactions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Transaksi Terbaru</h2>
                    <flux:button variant="ghost" size="sm" href="{{ route('transactions.index') }}">
                        Lihat Semua
                    </flux:button>
                </div>
                <div class="space-y-4">
                    @forelse($recentTransactions as $transaction)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $transaction->user->name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $transaction->book->title }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500 dark:text-gray-500">{{ $transaction->created_at->diffForHumans() }}</p>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($transaction->status === 'dipinjam') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                    @elseif($transaction->status === 'dikembalikan') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                    {{ ucfirst($transaction->status) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">Belum ada transaksi</p>
                    @endforelse
                </div>
            </div>

            <!-- Overdue Books -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Buku Terlambat</h2>
                    <flux:button variant="ghost" size="sm" href="{{ route('transactions.index', ['status' => 'terlambat']) }}">
                        Lihat Semua
                    </flux:button>
                </div>
                <div class="space-y-4">
                    @forelse($overdueTransactions as $transaction)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $transaction->user->name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $transaction->book->title }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-red-600 dark:text-red-400">
                                    {{ $transaction->due_date->diffForHumans() }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    {{ $transaction->daysLate() }} hari
                                </p>
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">Tidak ada buku yang terlambat</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Popular Books -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Buku Populer (30 Hari Terakhir)</h2>
                <flux:button variant="ghost" size="sm" href="{{ route('reports.analytics') }}">
                    Lihat Analytics
                </flux:button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                @forelse($popularBooks as $book)
                    <div class="text-center">
                        <div class="w-16 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-2 flex items-center justify-center">
                            @if($book->cover_image)
                                <img src="{{ Storage::url($book->cover_image) }}" alt="{{ $book->title }}" class="w-full h-full object-cover rounded-lg">
                            @else
                                <flux:icon.book-open class="w-8 h-8 text-gray-400" />
                            @endif
                        </div>
                        <p class="text-xs font-medium text-gray-900 dark:text-white truncate">{{ $book->title }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $book->transactions_count }} peminjaman</p>
                    </div>
                @empty
                    <div class="col-span-5">
                        <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">Belum ada data peminjaman</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Aksi Cepat</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <flux:button variant="outline" href="{{ route('transactions.create') }}" class="justify-start">
                    <flux:icon.plus class="w-4 h-4 mr-2" />
                    Pinjam Buku
                </flux:button>
                <flux:button variant="outline" href="{{ route('books.create') }}" class="justify-start">
                    <flux:icon.book-open class="w-4 h-4 mr-2" />
                    Tambah Buku
                </flux:button>
                <flux:button variant="outline" href="{{ route('users.create') }}" class="justify-start">
                    <flux:icon.user-plus class="w-4 h-4 mr-2" />
                    Tambah Anggota
                </flux:button>
                <flux:button variant="outline" href="{{ route('reports.index') }}" class="justify-start">
                    <flux:icon.chart-bar class="w-4 h-4 mr-2" />
                    Laporan
                </flux:button>
            </div>
        </div>
    </div>
</x-layouts.app>
