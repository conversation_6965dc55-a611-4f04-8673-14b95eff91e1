<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'staff', 'siswa'])->default('siswa')->after('email');
            $table->string('phone')->nullable()->after('role');
            $table->text('address')->nullable()->after('phone');
            $table->date('birth_date')->nullable()->after('address');
            $table->enum('gender', ['L', 'P'])->nullable()->after('birth_date');
            $table->string('student_id')->nullable()->unique()->after('gender');
            $table->string('class')->nullable()->after('student_id');
            $table->boolean('is_active')->default(true)->after('class');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role', 'phone', 'address', 'birth_date', 
                'gender', 'student_id', 'class', 'is_active'
            ]);
        });
    }
};
