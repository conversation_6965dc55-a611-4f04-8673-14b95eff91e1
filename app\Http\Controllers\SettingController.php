<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\ActivityLog;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    public function index()
    {
        $settings = Setting::all()->groupBy('group');
        return view('settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'required',
        ]);

        $oldValues = [];
        $newValues = [];

        foreach ($validated['settings'] as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                $oldValues[$key] = $setting->value;
                $newValues[$key] = $value;

                // Convert boolean values
                if ($setting->type === 'boolean') {
                    $value = $value ? '1' : '0';
                }

                $setting->update(['value' => $value]);
            }
        }

        ActivityLog::log('Mengubah pengaturan sistem', null, $oldValues, $newValues);

        return back()->with('success', 'Pengaturan berhasil diperbarui.');
    }
}
