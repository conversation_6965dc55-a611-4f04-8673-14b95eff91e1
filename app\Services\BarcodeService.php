<?php

namespace App\Services;

use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGeneratorPNG;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGeneratorSVG;

class BarcodeService
{
    /**
     * Generate barcode as PNG
     */
    public function generatePNG(string $code, int $width = 2, int $height = 30): string
    {
        $generator = new BarcodeGeneratorPNG();
        return $generator->getBarcode($code, $generator::TYPE_CODE_128, $width, $height);
    }

    /**
     * Generate barcode as SVG
     */
    public function generateSVG(string $code, int $width = 2, int $height = 30): string
    {
        $generator = new BarcodeGeneratorSVG();
        return $generator->getBarcode($code, $generator::TYPE_CODE_128, $width, $height);
    }

    /**
     * Generate barcode as base64 data URL
     */
    public function generateDataURL(string $code, int $width = 2, int $height = 30): string
    {
        $barcode = $this->generatePNG($code, $width, $height);
        return 'data:image/png;base64,' . base64_encode($barcode);
    }

    /**
     * Generate unique book barcode
     */
    public function generateBookBarcode(): string
    {
        do {
            $barcode = 'BK' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (\App\Models\Book::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Generate unique member card number
     */
    public function generateMemberCardNumber(): string
    {
        do {
            $cardNumber = 'MB' . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (\App\Models\User::where('student_id', $cardNumber)->exists());

        return $cardNumber;
    }

    /**
     * Validate barcode format
     */
    public function validateBarcode(string $barcode): bool
    {
        // Basic validation for book barcodes (BK + year + 6 digits)
        return preg_match('/^BK\d{4}\d{6}$/', $barcode) === 1;
    }
}
