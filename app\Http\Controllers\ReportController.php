<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Fine;
use App\Models\Category;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    public function index()
    {
        $stats = [
            'total_books' => Book::count(),
            'total_members' => User::where('role', 'siswa')->count(),
            'total_transactions' => Transaction::count(),
            'total_fines' => Fine::sum('amount'),
            'books_borrowed_today' => Transaction::whereDate('borrow_date', Carbon::today())->count(),
            'books_returned_today' => Transaction::whereDate('return_date', Carbon::today())->count(),
            'overdue_books' => Transaction::where('status', 'dipinjam')
                ->where('due_date', '<', Carbon::now())
                ->count(),
        ];

        return view('reports.index', compact('stats'));
    }

    public function books(Request $request)
    {
        $query = Book::with(['category']);

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('condition')) {
            $query->where('condition', $request->condition);
        }

        if ($request->filled('availability')) {
            if ($request->availability === 'available') {
                $query->where('available_stock', '>', 0);
            } else {
                $query->where('available_stock', '<=', 0);
            }
        }

        $books = $query->get();
        $categories = Category::all();

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('reports.books-pdf', compact('books'));
            return $pdf->download('laporan-buku-' . date('Y-m-d') . '.pdf');
        }

        if ($request->format === 'excel') {
            return Excel::download(new BooksExport($books), 'laporan-buku-' . date('Y-m-d') . '.xlsx');
        }

        return view('reports.books', compact('books', 'categories'));
    }

    public function transactions(Request $request)
    {
        $query = Transaction::with(['user', 'book', 'staff']);

        if ($request->filled('date_from')) {
            $query->whereDate('borrow_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('borrow_date', '<=', $request->date_to);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $transactions = $query->latest()->get();

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('reports.transactions-pdf', compact('transactions'));
            return $pdf->download('laporan-transaksi-' . date('Y-m-d') . '.pdf');
        }

        return view('reports.transactions', compact('transactions'));
    }

    public function fines(Request $request)
    {
        $query = Fine::with(['user', 'transaction.book']);

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $fines = $query->latest()->get();

        $stats = [
            'total_amount' => $fines->sum('amount'),
            'paid_amount' => $fines->where('status', 'sudah_bayar')->sum('amount'),
            'unpaid_amount' => $fines->where('status', 'belum_bayar')->sum('amount'),
            'waived_amount' => $fines->where('status', 'dibebaskan')->sum('amount'),
        ];

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('reports.fines-pdf', compact('fines', 'stats'));
            return $pdf->download('laporan-denda-' . date('Y-m-d') . '.pdf');
        }

        return view('reports.fines', compact('fines', 'stats'));
    }

    public function members(Request $request)
    {
        $query = User::where('role', 'siswa');

        if ($request->filled('class')) {
            $query->where('class', $request->class);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $members = $query->get();
        $classes = User::where('role', 'siswa')
            ->whereNotNull('class')
            ->distinct()
            ->pluck('class')
            ->sort();

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('reports.members-pdf', compact('members'));
            return $pdf->download('laporan-anggota-' . date('Y-m-d') . '.pdf');
        }

        return view('reports.members', compact('members', 'classes'));
    }

    public function analytics()
    {
        // Monthly transaction statistics
        $monthlyStats = Transaction::selectRaw('MONTH(borrow_date) as month, COUNT(*) as total')
            ->whereYear('borrow_date', Carbon::now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Popular books
        $popularBooks = Book::withCount(['transactions' => function ($query) {
                $query->where('created_at', '>=', Carbon::now()->subMonth());
            }])
            ->orderBy('transactions_count', 'desc')
            ->take(10)
            ->get();

        // Category statistics
        $categoryStats = Category::withCount('books')->get();

        // Member activity
        $memberActivity = User::where('role', 'siswa')
            ->withCount(['transactions' => function ($query) {
                $query->where('created_at', '>=', Carbon::now()->subMonth());
            }])
            ->orderBy('transactions_count', 'desc')
            ->take(10)
            ->get();

        return view('reports.analytics', compact(
            'monthlyStats',
            'popularBooks',
            'categoryStats',
            'memberActivity'
        ));
    }
}
