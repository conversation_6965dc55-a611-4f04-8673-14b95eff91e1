/* Dashboard Tabler-like Styles */

/* Page Header */
.page-header {
    background: #fff;
    border-bottom: 1px solid #e6e7e9;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
}

.page-pretitle {
    color: #6c757d;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    margin-bottom: 0.25rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Cards */
.card {
    background: #fff;
    border: 1px solid #e6e7e9;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-sm .card-body {
    padding: 1rem;
}

/* Avatar */
.avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Background Colors */
.bg-primary { background-color: #3b82f6 !important; }
.bg-success { background-color: #10b981 !important; }
.bg-warning { background-color: #f59e0b !important; }
.bg-danger { background-color: #ef4444 !important; }
.bg-info { background-color: #06b6d4 !important; }
.bg-purple { background-color: #8b5cf6 !important; }
.bg-teal { background-color: #14b8a6 !important; }
.bg-orange { background-color: #f97316 !important; }

/* Text Colors */
.text-white { color: #fff !important; }
.text-muted { color: #6c757d; }
.font-weight-medium { font-weight: 500; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.btn-primary {
    background-color: #3b82f6;
    color: #fff;
    border-color: #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    color: #fff;
    text-decoration: none;
}

.btn-success {
    background-color: #10b981;
    color: #fff;
    border-color: #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
    color: #fff;
    text-decoration: none;
}

.btn-outline {
    background-color: transparent;
    color: #6c757d;
    border-color: #e6e7e9;
}

.btn-outline:hover {
    background-color: #f8f9fa;
    color: #495057;
    text-decoration: none;
}

/* Layout */
.container-xl {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col, .col-auto, .col-sm-6, .col-lg-3, .col-lg-6 {
    padding: 0 0.75rem;
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
}

.col {
    flex: 1 0 0%;
}

.col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

.col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.row-deck {
    margin-bottom: 1.5rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-borrowed {
    background-color: #fef3c7;
    color: #92400e;
}

.status-returned {
    background-color: #d1fae5;
    color: #065f46;
}

.status-overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Book Cover */
.book-cover {
    width: 3rem;
    height: 4rem;
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

/* Icons */
.icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* Dark Mode */
.dark .page-header {
    background: #1f2937;
    border-color: #374151;
}

.dark .page-title {
    color: #f9fafb;
}

.dark .card {
    background: #1f2937;
    border-color: #374151;
}

.dark .text-muted {
    color: #9ca3af;
}

.dark .alert-danger {
    background-color: #7f1d1d;
    border-color: #991b1b;
    color: #fecaca;
}

.dark .alert-warning {
    background-color: #78350f;
    border-color: #92400e;
    color: #fed7aa;
}

.dark .btn-outline {
    color: #9ca3af;
    border-color: #374151;
}

.dark .btn-outline:hover {
    background-color: #374151;
    color: #f3f4f6;
}

/* Responsive */
@media (max-width: 576px) {
    .col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .container-xl {
        padding: 0 0.5rem;
    }
    
    .page-header {
        padding: 1rem 0;
    }
    
    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 992px) {
    .col-lg-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Utilities */
.align-items-center {
    align-items: center;
}

.justify-content-between {
    justify-content: space-between;
}

.justify-content-flex-start {
    justify-content: flex-start;
}

.d-flex {
    display: flex;
}

.gap-1 {
    gap: 0.5rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-size-lg {
    font-size: 1.5rem;
}

.font-size-sm {
    font-size: 0.875rem;
}

.font-size-xs {
    font-size: 0.75rem;
}
